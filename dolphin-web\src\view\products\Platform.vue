<template>
  <div id="ProductsPlatform">
    <div class="product-header">
      <h1 class="wow fadeInUp">AI模型训练平台</h1>
      <p class="wow fadeInUp" data-wow-delay="0.2s">
        专业的医疗AI模型训练平台，支持自定义数据集训练，快速构建专属医疗AI模型
      </p>
    </div>

    <div class="platform-workflow">
      <h2 class="wow fadeInUp">训练流程</h2>
      <div class="workflow-steps">
        <div class="step-item wow slideInUp">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>数据上传</h4>
            <p>支持DICOM、JPG等格式，自动数据预处理和质量检查</p>
          </div>
        </div>
        <div class="step-arrow">→</div>
        <div class="step-item wow slideInUp" data-wow-delay="0.1s">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>数据标注</h4>
            <p>智能辅助标注工具，支持多人协作标注和质量控制</p>
          </div>
        </div>
        <div class="step-arrow">→</div>
        <div class="step-item wow slideInUp" data-wow-delay="0.2s">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>模型训练</h4>
            <p>选择预训练模型，自动调参和训练，实时监控训练进度</p>
          </div>
        </div>
        <div class="step-arrow">→</div>
        <div class="step-item wow slideInUp" data-wow-delay="0.3s">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>模型部署</h4>
            <p>一键部署到云端或本地，提供API接口和SDK</p>
          </div>
        </div>
      </div>
    </div>

    <div class="platform-features">
      <h2 class="wow fadeInUp">平台优势</h2>
      <div class="row">
        <div class="col-md-6 col-sm-12">
          <div class="feature-card wow slideInLeft">
            <img src="@/assets/icons/top.png" alt="快速训练" class="feature-icon" />
            <h3>快速训练</h3>
            <p>基于预训练模型，大幅缩短训练时间，从数周缩短到数小时</p>
          </div>
        </div>
        <div class="col-md-6 col-sm-12">
          <div class="feature-card wow slideInRight">
            <img src="@/assets/icons/training.png" alt="自动调参" class="feature-icon" />
            <h3>自动调参</h3>
            <p>智能超参数优化，自动寻找最佳模型配置，无需专业知识</p>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 col-sm-12">
          <div class="feature-card wow slideInLeft" data-wow-delay="0.2s">
            <img src="@/assets/icons/service.png" alt="团队协作" class="feature-icon" />
            <h3>团队协作</h3>
            <p>支持多人协作标注和模型开发，权限管理和版本控制</p>
          </div>
        </div>
        <div class="col-md-6 col-sm-12">
          <div class="feature-card wow slideInRight" data-wow-delay="0.2s">
            <img src="@/assets/icons/csat.png" alt="性能监控" class="feature-icon" />
            <h3>性能监控</h3>
            <p>实时监控模型性能，提供详细的训练报告和评估指标</p>
          </div>
        </div>
      </div>
    </div>

    <div class="technical-specs">
      <h2 class="wow fadeInUp">技术规格</h2>
      <div class="specs-container wow fadeInUp" data-wow-delay="0.2s">
        <div class="spec-category">
          <h4>支持的模型类型</h4>
          <ul>
            <li>图像分类模型</li>
            <li>目标检测模型</li>
            <li>图像分割模型</li>
            <li>多模态融合模型</li>
          </ul>
        </div>
        <div class="spec-category">
          <h4>数据格式支持</h4>
          <ul>
            <li>DICOM医学影像</li>
            <li>JPG/PNG图像</li>
            <li>NIfTI格式</li>
            <li>自定义格式</li>
          </ul>
        </div>
        <div class="spec-category">
          <h4>计算资源</h4>
          <ul>
            <li>GPU集群训练</li>
            <li>分布式计算</li>
            <li>弹性资源调度</li>
            <li>成本优化</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="success-metrics">
      <h2 class="wow fadeInUp">平台成果</h2>
      <div class="metrics-grid wow fadeInUp" data-wow-delay="0.2s">
        <div class="metric-item">
          <div class="metric-number">500+</div>
          <div class="metric-label">训练模型数量</div>
        </div>
        <div class="metric-item">
          <div class="metric-number">50+</div>
          <div class="metric-label">合作医院</div>
        </div>
        <div class="metric-item">
          <div class="metric-number">95%</div>
          <div class="metric-label">平均准确率</div>
        </div>
        <div class="metric-item">
          <div class="metric-number">80%</div>
          <div class="metric-label">训练时间节省</div>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="cta-content wow fadeInUp">
        <h2>开始构建您的专属AI模型</h2>
        <p>申请平台试用，体验专业的医疗AI模型训练服务</p>
        <button class="btn btn-primary btn-lg">申请试用</button>
        <button class="btn btn-outline-primary btn-lg">预约演示</button>
      </div>
    </div>
  </div>
</template>

<script setup name="ProductsPlatform">
import WOW from 'wow.js'
import { onMounted } from 'vue'

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#ProductsPlatform {
  padding: 20px 0;
}

.product-header {
  text-align: center;
  margin-bottom: 60px;
}

.product-header h1 {
  color: #2c5aa0;
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.product-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.platform-workflow {
  margin-bottom: 60px;
}

.platform-workflow h2 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.workflow-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.step-item {
  text-align: center;
  max-width: 200px;
}

.step-number {
  width: 60px;
  height: 60px;
  background: #2c5aa0;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 15px;
}

.step-content h4 {
  color: #333;
  margin-bottom: 10px;
}

.step-content p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.step-arrow {
  font-size: 2rem;
  color: #2c5aa0;
  font-weight: bold;
}

.platform-features {
  margin-bottom: 60px;
}

.platform-features h2 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.feature-card {
  background: white;
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card i {
  font-size: 3rem;
  color: #2c5aa0;
  margin-bottom: 20px;
}

.feature-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 20px;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(1234%) hue-rotate(201deg) brightness(94%) contrast(90%);
}

.feature-card h3 {
  color: #333;
  margin-bottom: 15px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.technical-specs {
  background: #f8f9fa;
  padding: 60px 20px;
  margin-bottom: 60px;
  border-radius: 10px;
}

.technical-specs h2 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.specs-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 900px;
  margin: 0 auto;
}

.spec-category {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.spec-category h4 {
  color: #2c5aa0;
  margin-bottom: 20px;
  text-align: center;
}

.spec-category ul {
  list-style: none;
  padding: 0;
}

.spec-category li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.spec-category li:before {
  content: "•";
  color: #2c5aa0;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.success-metrics {
  margin-bottom: 60px;
}

.success-metrics h2 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.metric-item {
  text-align: center;
  padding: 30px 20px;
}

.metric-number {
  font-size: 3rem;
  font-weight: bold;
  color: #2c5aa0;
  margin-bottom: 10px;
}

.metric-label {
  color: #666;
  font-size: 1.1rem;
}

.cta-section {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
  border-radius: 10px;
}

.cta-content h2 {
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.cta-content .btn {
  margin: 0 10px;
  padding: 12px 30px;
}

.btn-outline-primary {
  border-color: white;
  color: white;
}

.btn-outline-primary:hover {
  background-color: white;
  color: #2c5aa0;
}

@media (max-width: 768px) {
  .workflow-steps {
    flex-direction: column;
  }

  .step-arrow {
    transform: rotate(90deg);
  }

  .product-header h1 {
    font-size: 2rem;
  }

  .cta-content .btn {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}
</style>
