<template>
  <div id="ProductsImaging">
    <div class="product-header">
      <h1 class="wow fadeInUp">医疗影像智能工具</h1>
      <p class="wow fadeInUp" data-wow-delay="0.2s">
        专业的医疗超声影像处理工具套件，集成AI算法，提升影像质量和诊断效率
      </p>
    </div>

    <div class="tools-showcase">
      <div class="row">
        <div class="col-md-6 col-sm-12">
          <div class="tool-card wow slideInLeft">
            <div class="tool-image">
              <img src="@/assets/icons/size.png" alt="影像增强" class="tool-icon" />
            </div>
            <h3>影像增强工具</h3>
            <p>基于深度学习的影像去噪、对比度增强和细节优化，显著提升影像质量</p>
            <ul>
              <li>智能去噪算法</li>
              <li>自适应对比度调整</li>
              <li>边缘细节增强</li>
              <li>实时处理预览</li>
            </ul>
          </div>
        </div>
        <div class="col-md-6 col-sm-12">
          <div class="tool-card wow slideInRight">
            <div class="tool-image">
              <img src="@/assets/icons/cancer.png" alt="病灶标注" class="tool-icon" />
            </div>
            <h3>病灶标注工具</h3>
            <p>智能辅助标注系统，自动识别并标记可疑区域，支持多种标注模式</p>
            <ul>
              <li>自动病灶检测</li>
              <li>智能轮廓描绘</li>
              <li>多类型标注支持</li>
              <li>标注数据导出</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 col-sm-12">
          <div class="tool-card wow slideInLeft" data-wow-delay="0.2s">
            <div class="tool-image">
              <img src="@/assets/icons/size.png" alt="测量分析" class="tool-icon" />
            </div>
            <h3>测量分析工具</h3>
            <p>精确的影像测量功能，支持距离、面积、体积等多维度测量分析</p>
            <ul>
              <li>多种测量模式</li>
              <li>自动校准功能</li>
              <li>统计分析报告</li>
              <li>历史数据对比</li>
            </ul>
          </div>
        </div>
        <div class="col-md-6 col-sm-12">
          <div class="tool-card wow slideInRight" data-wow-delay="0.2s">
            <div class="tool-image">
              <img src="@/assets/icons/RS.png" alt="报告生成" class="tool-icon" />
            </div>
            <h3>报告生成工具</h3>
            <p>自动生成标准化医疗报告，支持模板定制和多格式导出</p>
            <ul>
              <li>智能报告生成</li>
              <li>模板自定义</li>
              <li>多格式导出</li>
              <li>电子签名支持</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="features-section">
      <h2 class="wow fadeInUp">产品特色</h2>
      <div class="row">
        <div class="col-md-4 col-sm-12">
          <div class="feature-box wow zoomIn">
            <i class="fas fa-bolt"></i>
            <h4>高效处理</h4>
            <p>GPU加速处理，单张影像处理时间小于1秒</p>
          </div>
        </div>
        <div class="col-md-4 col-sm-12">
          <div class="feature-box wow zoomIn" data-wow-delay="0.1s">
            <i class="fas fa-shield-alt"></i>
            <h4>数据安全</h4>
            <p>本地部署，数据不出院，符合医疗数据安全规范</p>
          </div>
        </div>
        <div class="col-md-4 col-sm-12">
          <div class="feature-box wow zoomIn" data-wow-delay="0.2s">
            <i class="fas fa-plug"></i>
            <h4>易于集成</h4>
            <p>标准化接口，快速集成到现有PACS系统</p>
          </div>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="cta-content wow fadeInUp">
        <h2>体验智能影像工具</h2>
        <p>申请产品演示，了解如何提升您的影像诊断效率</p>
        <button class="btn btn-primary btn-lg">申请演示</button>
        <button class="btn btn-outline-primary btn-lg">下载手册</button>
      </div>
    </div>
  </div>
</template>

<script setup name="ProductsImaging">
import WOW from 'wow.js'
import { onMounted } from 'vue'

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#ProductsImaging {
  padding: 20px 0;
}

.product-header {
  text-align: center;
  margin-bottom: 50px;
}

.product-header h1 {
  color: #2c5aa0;
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.product-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.tools-showcase {
  margin-bottom: 60px;
}

.tool-card {
  background: white;
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-5px);
}

.tool-image {
  text-align: center;
  margin-bottom: 20px;
}

.tool-image i {
  font-size: 3rem;
  color: #2c5aa0;
}

.tool-icon {
  width: 48px;
  height: 48px;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(1234%) hue-rotate(201deg) brightness(94%) contrast(90%);
}

.tool-card h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.tool-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tool-card ul {
  list-style: none;
  padding: 0;
}

.tool-card li {
  padding: 5px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.tool-card li:before {
  content: "✓";
  color: #2c5aa0;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.features-section {
  background: #f8f9fa;
  padding: 60px 20px;
  margin-bottom: 50px;
  border-radius: 10px;
}

.features-section h2 {
  text-align: center;
  color: #333;
  margin-bottom: 50px;
}

.feature-box {
  text-align: center;
  padding: 30px 20px;
}

.feature-box i {
  font-size: 3rem;
  color: #2c5aa0;
  margin-bottom: 20px;
}

.feature-box h4 {
  color: #333;
  margin-bottom: 15px;
}

.feature-box p {
  color: #666;
  line-height: 1.6;
}

.cta-section {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
  border-radius: 10px;
}

.cta-content h2 {
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.cta-content .btn {
  margin: 0 10px;
  padding: 12px 30px;
}

.btn-outline-primary {
  border-color: white;
  color: white;
}

.btn-outline-primary:hover {
  background-color: white;
  color: #2c5aa0;
}

@media (max-width: 768px) {
  .product-header h1 {
    font-size: 2rem;
  }

  .cta-content .btn {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}
</style>
