import { createRouter as _createRouter, createWebHashHistory } from 'vue-router'

// const pages = import.meta.glob('../src/pages/**/*.vue')
// const routes = Object.keys(pages).map((path)=>{
//   let name = path.match(/\/pages(.*)\.vue$/)[1].toLowerCase();
//   if(name.substring(name.length - 5) == 'index'){
//     name = name.slice(0, -5);//去掉最后的index
//   }
//   return {
//     path: name === '/home' ? '/': name,
//     component: pages[path]
//   }
// })

const routes = [
  {
    path: '/',
    name: 'PageView',
    redirect: '/home',
    component: () => import('@/view/PageView.vue'),
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/view/home/<USER>'),
        meta: {
          title: '首页 - 海宁海豚之声医疗科技有限公司'
        }
      },
      {
        path: '/products',
        name: 'products',
        component: () => import('@/view/products/index.vue'),
        meta: {
          title: '产品中心'
        }
      },
      {
        path: '/products/api',
        name: 'productsApi',
        component: () => import('@/view/products/ApiProduct.vue'),
        meta: {
          title: '产品中心丨大模型API'
        }
      },
      {
        path: '/products/imaging',
        name: 'productsImaging',
        component: () => import('@/view/products/ImagingTools.vue'),
        meta: {
          title: '产品中心丨医疗影像工具'
        }
      },
      {
        path: '/products/platform',
        name: 'productsPlatform',
        component: () => import('@/view/products/Platform.vue'),
        meta: {
          title: '产品中心丨模型训练平台'
        }
      },
      {
        path: '/solutions',
        name: 'solutions',
        component: () => import('@/view/solutions/index.vue'),
        props: true,
        meta: {
          title: '解决方案'
        }
      },
      {
        path: '/solutiondetail',
        name: 'solutionDetail',
        component: () => import('@/view/solutions/Detail.vue'),
        props: true,
        meta: {
          title: '解决方案-详情'
        }
      },
      {
        path: '/cases',
        name: 'cases',
        component: () => import('@/view/cases/index.vue'),
        meta: {
          title: '成功案例'
        }
      },
      {
        path: '/news',
        name: 'news',
        component: () => import('@/view/news/index.vue'),
        meta: {
          title: '新闻资讯'
        }
      },
      {
        path: '/about',
        name: 'about',
        component: () => import('@/view/about/index.vue'),
        meta: {
          title: '关于我们'
        }
      },

      {
        path: '/contact',
        name: 'contact',
        component: () => import('@/view/contact/index.vue'),
        meta: {
          title: '联系我们'
        }
      }
    ]
  }
]

export function createRouter() {
  return _createRouter({
    history: createWebHashHistory(),
    routes
  })
}

