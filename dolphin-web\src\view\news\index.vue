<template>
  <div id="News">
    <!-- Banner区域 -->
    <div class="news-banner">
      <div class="banner-overlay">
        <div class="banner-content">
          <h1 class="banner-title">新闻中心</h1>
          <p class="banner-subtitle">NEWS CENTER</p>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- 导航按钮区域 -->
      <div class="news-nav">
        <div class="nav-buttons">
          <button
            :class="{ active: activeTab === 'company' }"
            @click="activeTab = 'company'"
            class="nav-btn"
          >
            公司新闻
          </button>
          <button
            :class="{ active: activeTab === 'industry' }"
            @click="activeTab = 'industry'"
            class="nav-btn"
          >
            行业动态
          </button>
          <button
            :class="{ active: activeTab === 'tech' }"
            @click="activeTab = 'tech'"
            class="nav-btn"
          >
            技术发布
          </button>
        </div>
      </div>

      <!-- 新闻卡片网格 -->
      <div class="news-grid">
        <div
          v-for="(item, index) in filteredNews"
          :key="index"
          class="parent wow fadeInUp"
          :data-wow-delay="`${index * 0.1}s`"
        >
          <div class="card">
            <div class="content-box">
              <span class="card-title">{{ item.title }}</span>
              <p class="card-content">{{ item.summary }}</p>
              <span class="see-more">查看详情</span>
            </div>
            <div class="date-box">
              <span class="month">{{ item.dateTag.split('-')[0] }}</span>
              <span class="date">{{ item.dateTag.split('-')[1] }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="News">
import { ref, computed, onMounted } from 'vue'
import WOW from 'wow.js'

const activeTab = ref('company')

const newsList = [
  {
    type: 'company',
    title: '海豚之声医疗科技获得国家高新技术企业认定',
    summary:
      '我公司凭借在医疗AI领域的技术创新和产业化成果，成功获得国家高新技术企业认定，这标志着公司在技术创新方面的实力得到了权威认可。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'company',
    title: '与北京协和医院签署战略合作协议',
    summary:
      '公司与北京协和医院正式签署战略合作协议，双方将在医疗AI技术研发、临床应用和人才培养等方面开展深度合作。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'company',
    title: '海豚之声完成B轮融资',
    summary:
      '公司成功完成B轮融资，本轮融资将主要用于技术研发、市场拓展和团队建设，进一步巩固公司在医疗AI领域的领先地位。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'tech',
    title: '发布超声影像大模型V2.0版本',
    summary:
      '公司正式发布超声影像大模型V2.0版本，新版本在诊断准确率、处理速度和适用范围等方面都有显著提升，支持更多疾病类型的智能诊断。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'tech',
    title: '多模态医疗AI平台正式上线',
    summary:
      '公司自主研发的多模态医疗AI平台正式上线，该平台整合了影像、文本、语音等多种数据类型，为医疗机构提供更全面的AI解决方案。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'tech',
    title: 'AI辅助诊断系统获得医疗器械认证',
    summary:
      '经过严格的临床试验和技术审核，我公司的AI辅助诊断系统正式获得国家医疗器械认证，可在医疗机构正式投入使用。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'industry',
    title: '医疗AI行业迎来政策利好',
    summary:
      '国家卫健委发布新政策，鼓励医疗AI技术在临床诊疗中的应用，为医疗AI行业发展提供了强有力的政策支持。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'industry',
    title: '全球医疗AI市场规模持续增长',
    summary:
      '据最新研究报告显示，全球医疗AI市场规模预计将在未来五年内保持高速增长，其中医学影像AI应用占据重要地位。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  },
  {
    type: 'industry',
    title: '超声AI技术标准化进程加速',
    summary:
      '行业专家呼吁建立统一的超声AI技术标准，推动医疗AI技术的规范化发展，提高诊断的准确性和一致性。',
    dateTag: '01-12',
    image: '', // 图片路径待补充
    time: '2024-01-12'
  }
]

const filteredNews = computed(() => {
  if (activeTab.value === 'company') {
    return newsList.filter((item) => item.type === 'company')
  } else if (activeTab.value === 'industry') {
    return newsList.filter((item) => item.type === 'industry')
  } else if (activeTab.value === 'tech') {
    return newsList.filter((item) => item.type === 'tech')
  }
  return newsList
})

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#News {
  background-color: #f8f9fa;
}

/* Banner区域样式 */
.news-banner {
  position: relative;
  height: 100vh;
  background-image: url('@/assets/img/news/banner.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-content {
  text-align: center;
  color: white;
}

.banner-title {
  font-size: 50px;
  font-weight: 600;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.95);
}

.banner-subtitle {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 2px;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.9);
}

/* 导航区域样式 */
.news-nav {
  padding: 80px 0 60px 0;
  display: flex;
  justify-content: center;
}

.nav-buttons {
  display: flex;
  gap: 25px;
}

.nav-btn {
  font-size: 16px;
  padding: 1em 3.3em;
  cursor: pointer;
  transform: perspective(200px) rotateX(15deg);
  color: white;
  font-weight: 900;
  border: none;
  border-radius: 5px;
  background: linear-gradient(
    0deg,
    rgba(63, 94, 251, 1) 0%,
    rgba(70, 135, 252, 1) 100%
  );
  box-shadow: rgba(63, 94, 251, 0.2) 0px 40px 29px 0px;
  will-change: transform;
  transition: all 0.3s;
  border-bottom: 2px solid rgba(70, 135, 252, 1);
}

.nav-btn:hover {
  transform: perspective(180px) rotateX(30deg) translateY(2px);
}

.nav-btn:active,
.nav-btn.active {
  transform: perspective(170px) rotateX(36deg) translateY(5px);
}

/* 新闻网格布局 */
.news-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto 80px auto;
  padding: 0 20px;
}

/* 3D卡片样式 */
.parent {
  width: 100%;
  padding: 20px;
  perspective: 1000px;
}

.card {
  padding-top: 60px;
  border: 3px solid rgb(255, 255, 255);
  transform-style: preserve-3d;
  background: linear-gradient(135deg, #0000 18.75%, #f3f3f3 0 31.25%, #0000 0),
    repeating-linear-gradient(45deg, #f3f3f3 -6.25% 6.25%, #ffffff 0 18.75%);
  background-size: 60px 60px;
  background-position: 0 0, 0 0;
  background-color: #f0f0f0;
  width: 100%;
  height: 420px;
  box-shadow: rgba(142, 142, 142, 0.3) 0px 30px 30px -10px;
  transition: all 0.5s ease-in-out;
}

.card:hover {
  background-position: -100px 100px, -100px 100px;
  transform: rotate3d(0.5, 1, 0, 30deg);
}

.content-box {
  background: rgba(4, 193, 250, 0.732);
  transition: all 0.5s ease-in-out;
  padding: 70px 30px 30px 30px;
  transform-style: preserve-3d;
}

.content-box .card-title {
  color: white;
  font-size: 20px;
  font-weight: 900;
  transition: all 0.5s ease-in-out;
  transform: translate3d(0px, 0px, 50px);
  margin-bottom: 15px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-box .card-title:hover {
  transform: translate3d(0px, 0px, 60px);
}

.content-box .card-content {
  margin-top: 15px;
  font-size: 14px;
  font-weight: 700;
  color: #f2f2f2;
  transition: all 0.5s ease-in-out;
  transform: translate3d(0px, 0px, 30px);
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-box .card-content:hover {
  transform: translate3d(0px, 0px, 60px);
}

.content-box .see-more {
  cursor: pointer;
  margin-top: 1.5rem;
  display: inline-block;
  font-weight: 900;
  font-size: 11px;
  text-transform: uppercase;
  color: rgb(7, 185, 255);
  background: white;
  padding: 0.6rem 0.8rem;
  transition: all 0.5s ease-in-out;
  transform: translate3d(0px, 0px, 20px);
}

.content-box .see-more:hover {
  transform: translate3d(0px, 0px, 60px);
}

.date-box {
  position: absolute;
  top: 35px;
  right: 35px;
  height: 70px;
  width: 70px;
  background: white;
  border: 1px solid rgb(7, 185, 255);
  padding: 12px;
  transform: translate3d(0px, 0px, 80px);
  box-shadow: rgba(100, 100, 111, 0.2) 0px 17px 10px -10px;
}

.date-box span {
  display: block;
  text-align: center;
}

.date-box .month {
  color: rgb(4, 193, 250);
  font-size: 9px;
  font-weight: 700;
}

.date-box .date {
  font-size: 20px;
  font-weight: 900;
  color: rgb(4, 193, 250);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .news-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 35px;
    padding: 0 15px;
  }

  .parent {
    padding: 15px;
  }

  .card:hover {
    transform: rotate3d(0.5, 1, 0, 15deg);
  }

  .news-nav {
    padding: 60px 0 50px 0;
  }

  .nav-buttons {
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }

  .banner-title {
    font-size: 40px;
  }

  .banner-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .news-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 10px;
  }

  .parent {
    padding: 10px;
  }

  .card {
    padding-top: 50px;
    height: 380px;
  }

  .card:hover {
    transform: rotate3d(0.5, 1, 0, 10deg);
  }

  .content-box {
    padding: 60px 25px 25px 25px;
  }

  .content-box .card-title {
    font-size: 18px;
  }

  .date-box {
    top: 25px;
    right: 25px;
    height: 60px;
    width: 60px;
    padding: 10px;
  }

  .date-box .month {
    font-size: 9px;
  }

  .date-box .date {
    font-size: 18px;
  }

  .news-banner {
    height: 80vh;
  }

  .banner-title {
    font-size: 35px;
  }

  .banner-subtitle {
    font-size: 14px;
    letter-spacing: 1px;
  }

  .news-nav {
    padding: 50px 0 40px 0;
  }

  .nav-buttons {
    gap: 15px;
  }

  .nav-btn {
    padding: 0.8em 2.5em;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .parent {
    padding: 8px;
  }

  .card {
    padding-top: 40px;
    height: 350px;
  }

  .card:hover {
    transform: rotate3d(0.5, 1, 0, 5deg);
  }

  .content-box {
    padding: 50px 20px 20px 20px;
  }

  .content-box .card-title {
    font-size: 16px;
  }

  .content-box .card-content {
    font-size: 12px;
  }

  .content-box .see-more {
    font-size: 9px;
    padding: 0.5rem 0.7rem;
  }

  .date-box {
    top: 20px;
    right: 20px;
    height: 50px;
    width: 50px;
    padding: 8px;
  }

  .date-box .month {
    font-size: 8px;
  }

  .date-box .date {
    font-size: 16px;
  }

  .news-banner {
    height: 70vh;
  }

  .banner-title {
    font-size: 30px;
  }

  .banner-subtitle {
    font-size: 12px;
    letter-spacing: 0.5px;
  }

  .news-nav {
    padding: 40px 0 30px 0;
  }

  .nav-buttons {
    width: 100%;
    justify-content: space-between;
    gap: 12px;
  }

  .nav-btn {
    flex: 1;
    text-align: center;
    padding: 0.7em 1.8em;
    font-size: 13px;
  }

  .news-grid {
    gap: 25px;
    margin-bottom: 60px;
  }
}
</style>
