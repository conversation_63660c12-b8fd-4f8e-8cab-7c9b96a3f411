# HTTP server (可选：重定向到 HTTPS)
server {
    listen 80;
    server_name dolphin-ai.cn;

    location / {
        return 301 https://$host$request_uri;
    }

    location /api {
        return 301 https://$host$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl;
    server_name dolphin-ai.cn;

    ssl_certificate /etc/nginx/ssl/dolphin-ai.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/dolphin-ai.cn/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    root /usr/share/nginx/html/;
    resolver *************** valid=30s;

    index index.html;
}