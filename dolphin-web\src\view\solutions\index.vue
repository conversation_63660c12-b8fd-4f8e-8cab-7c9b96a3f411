<template>
  <div id="Solutions">
    <!-- Banner区域 -->
    <div class="solutions-banner">
      <div class="banner-overlay">
        <div class="banner-content">
          <h1 class="banner-title">解决方案</h1>
          <p class="banner-subtitle">Medical AI Solutions for Healthcare</p>
        </div>
      </div>
    </div>

    <div class="solutions-section">
      <div class="container">
        <div class="solutions-container row">
          <div
            class="solution-item col-xs-12 col-sm-6 col-md-4 wow slideInUp"
            v-for="(item, index) in solutionsList"
            :key="index"
            @click="solutionClick(item.id)"
          >
            <div class="card">
              <div class="content">
                <svg
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 9V5H4V9H20ZM20 11H4V19H20V11ZM3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21 21 21 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3ZM5 12H8V17H5V12ZM5 6H7V8H5V6ZM9 6H11V8H9V6Z"
                  ></path>
                </svg>
                <p class="para">
                  {{ item.title }}
                </p>
                <p class="eng-title">
                  {{ item.eng_title }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Solutions">
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import WOW from 'wow.js'
import solution1 from '@/assets/img/service1.jpg'
import solution2 from '@/assets/img/service2.jpg'
import solution3 from '@/assets/img/service3.jpg'

const solutionsList = [
  {
    id: 'hospital-system',
    title: '医院辅助诊断系统',
    eng_title: 'Hospital AI Assistant',
    img: solution1
  },
  {
    id: 'research-platform',
    title: '医学科研平台',
    eng_title: 'Research Platform',
    img: solution2
  },
  {
    id: 'workflow-optimization',
    title: '医生工作流优化',
    eng_title: 'Workflow Optimization',
    img: solution3
  }
]

const router = useRouter()

function solutionClick(id) {
  router.push({
    name: 'solutionDetail',
    state: {
      id
    }
  })
}

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#Solutions {
  background-color: #f8f9fa;
}

.solutions-section {
  background: linear-gradient(
    135deg,
    #0c1445 0%,
    #1a237e 25%,
    #283593 50%,
    #3949ab 75%,
    #5c6bc0 100%
  );
  position: relative;
  overflow: hidden;
}

.solutions-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(2px 2px at 20px 30px, #ffffff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #ffffff, transparent),
    radial-gradient(
      1px 1px at 130px 80px,
      rgba(255, 255, 255, 0.6),
      transparent
    ),
    radial-gradient(2px 2px at 160px 30px, #ffffff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 20s linear infinite;
  opacity: 0.6;
}

.solutions-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(1px 1px at 50px 50px, #ffffff, transparent),
    radial-gradient(
      1px 1px at 100px 100px,
      rgba(255, 255, 255, 0.7),
      transparent
    ),
    radial-gradient(1px 1px at 150px 25px, #ffffff, transparent),
    radial-gradient(
      1px 1px at 200px 75px,
      rgba(255, 255, 255, 0.5),
      transparent
    );
  background-repeat: repeat;
  background-size: 250px 150px;
  animation: sparkle 25s linear infinite reverse;
  opacity: 0.4;
}

@keyframes sparkle {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(-200px) translateY(-100px);
  }
}

/* Banner区域样式 */
.solutions-banner {
  position: relative;
  height: 100vh;
  background-image: url('@/assets/img/solutions/img15.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-content {
  text-align: center;
  color: white;
}

.banner-title {
  font-size: 50px;
  font-weight: 600;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.95);
}

.banner-subtitle {
  font-size: 20px;
  font-weight: 300;
  margin: 0;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.6);
  color: rgba(255, 255, 255, 0.9);
}

.solutions-container {
  padding: 80px 50px;
  margin-top: 0;
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.solution-item {
  margin-bottom: 50px;
  display: flex;
  justify-content: center;
}

.card {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 320px;
  border-radius: 24px;
  line-height: 1.6;
  transition: all 0.48s cubic-bezier(0.23, 1, 0.32, 1);
  cursor: pointer;
  z-index: 2;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  padding: 48px;
  border-radius: 22px;
  color: #ffffff;
  overflow: hidden;
  background: #0a3cff;
  transition: all 0.48s cubic-bezier(0.23, 1, 0.32, 1);
  width: 100%;
  height: 100%;
}

.content::before {
  position: absolute;
  content: '';
  top: -4%;
  left: 50%;
  width: 90%;
  height: 90%;
  transform: translate(-50%);
  background: #ced8ff;
  z-index: -1;
  transform-origin: bottom;
  border-radius: inherit;
  transition: all 0.48s cubic-bezier(0.23, 1, 0.32, 1);
}

.content::after {
  position: absolute;
  content: '';
  top: -8%;
  left: 50%;
  width: 80%;
  height: 80%;
  transform: translate(-50%);
  background: #e7ecff;
  z-index: -2;
  transform-origin: bottom;
  border-radius: inherit;
  transition: all 0.48s cubic-bezier(0.23, 1, 0.32, 1);
}

.content svg {
  width: 64px;
  height: 64px;
}

.content .para {
  z-index: 1;
  opacity: 1;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  transition: all 0.48s cubic-bezier(0.23, 1, 0.32, 1);
}

.content .eng-title {
  z-index: 1;
  color: #fea000;
  font-family: inherit;
  font-size: 18px;
  margin: 0;
  transition: all 0.48s cubic-bezier(0.23, 1, 0.32, 1);
}

.card:hover {
  transform: translate(0px, -16px);
}

.card:hover .content::before {
  rotate: -8deg;
  top: 0;
  width: 100%;
  height: 100%;
}

.card:hover .content::after {
  rotate: 8deg;
  top: 0;
  width: 100%;
  height: 100%;
}

@media screen and (max-width: 1200px) {
  .solutions-container {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    max-width: 1200px;
  }
}

@media screen and (max-width: 768px) {
  .banner-title {
    font-size: 36px;
  }

  .banner-subtitle {
    font-size: 16px;
  }

  .solutions-container {
    padding: 40px 15px;
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 100%;
  }

  .solution-item {
    margin-bottom: 30px;
  }

  .card {
    width: 320px;
    height: 280px;
  }

  .content {
    padding: 36px;
    gap: 20px;
  }

  .content svg {
    width: 48px;
    height: 48px;
  }

  .content .para {
    font-size: 20px;
  }

  .content .eng-title {
    font-size: 16px;
  }
}
</style>
