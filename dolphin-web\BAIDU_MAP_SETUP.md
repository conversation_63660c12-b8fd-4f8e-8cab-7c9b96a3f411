# 百度地图API配置指南

## 🚨 问题说明
当前项目中的百度地图API密钥已过期或受限，导致控制台出现错误：
```
APP服务异常，详情查看: http://lbsyun.baidu.com/apiconsole/key#.
```

## 🔧 解决方案

### 方案一：申请新的百度地图API密钥（推荐）

#### 1. 访问百度地图开放平台
- 网址：https://lbsyun.baidu.com/
- 注册或登录您的百度账号

#### 2. 创建应用
1. 进入控制台：https://lbsyun.baidu.com/apiconsole/key
2. 点击"创建应用"
3. 填写应用信息：
   - **应用名称**：海宁海豚之声医疗科技官网
   - **应用类型**：浏览器端
   - **启用服务**：勾选"地图JavaScript API v3.0"
   - **Referer白名单**：
     ```
     http://localhost:5173/*
     http://127.0.0.1:5173/*
     https://yourdomain.com/*
     ```

#### 3. 获取API Key (AK)
- 创建成功后，复制生成的API Key

#### 4. 配置到项目中
1. 打开 `.env.development` 文件
2. 将 `YOUR_BAIDU_MAP_API_KEY_HERE` 替换为您的API Key：
   ```
   VITE_BAIDU_MAP_AK=您的API密钥
   ```
3. 同样更新 `.env.production` 文件

#### 5. 启用地图功能
1. 打开 `index.html` 文件
2. 取消注释百度地图API脚本：
   ```html
   <script
     type="text/javascript"
     src="https://api.map.baidu.com/api?v=3.0&ak=<%= VITE_BAIDU_MAP_AK %>"
   ></script>
   ```

### 方案二：使用当前的静态地图显示（已实现）

我已经临时禁用了百度地图API，并实现了静态地图显示：
- ✅ 移除了API密钥错误
- ✅ 显示公司地址信息
- ✅ 提供外部地图链接
- ✅ 保持页面美观

## 📍 当前地图功能

### 联系我们页面 (`/contact`)
- 显示公司完整地址信息
- 提供百度地图外部链接
- 美观的静态地图样式

### ContactUs页面 (`/contactus`)
- 同样的静态地图显示
- 包含联系方式信息

## 🔄 如何恢复完整地图功能

当您获得新的API密钥后：

1. **更新环境变量**：
   ```bash
   # .env.development
   VITE_BAIDU_MAP_AK=您的新API密钥
   ```

2. **启用HTML中的地图脚本**：
   ```html
   <!-- 取消注释这行 -->
   <script
     type="text/javascript"
     src="https://api.map.baidu.com/api?v=3.0&ak=<%= VITE_BAIDU_MAP_AK %>"
   ></script>
   ```

3. **恢复ContactUs组件中的地图代码**：
   ```javascript
   onMounted(() => {
     BMap = window.BMap
     var map = new BMap.Map('map')
     var point = new BMap.Point(120.6816, 30.5269)
     map.centerAndZoom(point, 18)
     // ... 其他地图配置
   })
   ```

## 📝 注意事项

1. **API配额限制**：免费版本有调用次数限制
2. **域名白名单**：确保将所有访问域名添加到白名单
3. **HTTPS要求**：生产环境建议使用HTTPS
4. **备案要求**：如果是中国大陆网站，可能需要备案

## 🆘 常见问题

### Q: 为什么地图不显示？
A: 检查API密钥是否正确，域名是否在白名单中

### Q: 控制台还有错误？
A: 确保完全重启开发服务器，清除浏览器缓存

### Q: 生产环境地图不工作？
A: 检查生产环境的域名是否添加到白名单

## 📞 技术支持

如果遇到问题，可以：
1. 查看百度地图API文档：https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference_3_0.html
2. 联系百度地图技术支持
3. 或继续使用当前的静态地图显示方案
