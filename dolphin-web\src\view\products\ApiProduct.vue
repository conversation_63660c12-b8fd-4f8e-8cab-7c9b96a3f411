<template>
  <div id="ProductsApi">
    <div class="product-header">
      <h1 class="wow fadeInUp">医疗超声大模型API</h1>
      <p class="wow fadeInUp" data-wow-delay="0.2s">
        基于深度学习的医疗超声影像分析API，提供高精度的病灶检测、测量和诊断辅助功能
      </p>
    </div>

    <div class="product-features">
      <h2 class="wow fadeInUp">核心功能</h2>
      <div class="row">
        <div class="col-md-6 col-sm-12 feature-item wow slideInLeft">
          <div class="feature-icon">
            <i class="fas fa-brain"></i>
          </div>
          <h3>智能病灶检测</h3>
          <p>基于大模型的超声影像病灶自动识别，准确率达95%以上，支持多种器官病变检测</p>
        </div>
        <div class="col-md-6 col-sm-12 feature-item wow slideInRight">
          <div class="feature-icon">
            <i class="fas fa-ruler"></i>
          </div>
          <h3>精准测量分析</h3>
          <p>自动测量病灶大小、形态特征，生成标准化报告，提高诊断效率和准确性</p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 col-sm-12 feature-item wow slideInLeft" data-wow-delay="0.2s">
          <div class="feature-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>趋势分析</h3>
          <p>对比历史影像数据，分析病情发展趋势，为医生提供更全面的诊断依据</p>
        </div>
        <div class="col-md-6 col-sm-12 feature-item wow slideInRight" data-wow-delay="0.2s">
          <div class="feature-icon">
            <i class="fas fa-code"></i>
          </div>
          <h3>简单集成</h3>
          <p>RESTful API接口，支持多种编程语言，快速集成到现有医疗系统中</p>
        </div>
      </div>
    </div>

    <div class="api-specs">
      <h2 class="wow fadeInUp">技术规格</h2>
      <div class="specs-grid wow fadeInUp" data-wow-delay="0.2s">
        <div class="spec-item">
          <h4>响应时间</h4>
          <p>&lt; 2秒</p>
        </div>
        <div class="spec-item">
          <h4>准确率</h4>
          <p>95%+</p>
        </div>
        <div class="spec-item">
          <h4>支持格式</h4>
          <p>DICOM, JPG, PNG</p>
        </div>
        <div class="spec-item">
          <h4>并发处理</h4>
          <p>1000+ QPS</p>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="cta-content wow fadeInUp">
        <h2>立即体验医疗超声大模型API</h2>
        <p>申请免费试用，体验AI赋能的医疗影像分析</p>
        <button class="btn btn-primary btn-lg">申请试用</button>
        <button class="btn btn-outline-primary btn-lg">查看文档</button>
      </div>
    </div>
  </div>
</template>

<script setup name="ProductsApi">
import WOW from 'wow.js'
import { onMounted } from 'vue'

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#ProductsApi {
  padding: 20px 0;
}

.product-header {
  text-align: center;
  margin-bottom: 50px;
}

.product-header h1 {
  color: #2c5aa0;
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.product-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.product-features {
  margin-bottom: 50px;
}

.product-features h2 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.feature-item {
  text-align: center;
  padding: 30px 20px;
  margin-bottom: 30px;
}

.feature-icon {
  font-size: 3rem;
  color: #2c5aa0;
  margin-bottom: 20px;
}

.feature-item h3 {
  color: #333;
  margin-bottom: 15px;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

.api-specs {
  background: #f8f9fa;
  padding: 50px 20px;
  margin-bottom: 50px;
  border-radius: 10px;
}

.api-specs h2 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.spec-item {
  text-align: center;
  background: white;
  padding: 30px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.spec-item h4 {
  color: #2c5aa0;
  margin-bottom: 10px;
}

.spec-item p {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.cta-section {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 60px 20px;
  text-align: center;
  border-radius: 10px;
}

.cta-content h2 {
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.cta-content .btn {
  margin: 0 10px;
  padding: 12px 30px;
}

.btn-outline-primary {
  border-color: white;
  color: white;
}

.btn-outline-primary:hover {
  background-color: white;
  color: #2c5aa0;
}

@media (max-width: 768px) {
  .product-header h1 {
    font-size: 2rem;
  }
  
  .specs-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-content .btn {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}
</style>
