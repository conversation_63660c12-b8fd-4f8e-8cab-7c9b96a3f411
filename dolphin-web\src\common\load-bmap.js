// 高德静态地图配置
const AMAP_CONFIG = {
  key: 'dfdb19e4243ac4809103956dc15bc845',
  baseUrl: 'https://restapi.amap.com/v3/staticmap'
}

/**
 * 生成高德静态地图URL
 * @param {Object} options 地图配置选项
 * @param {string} options.location 地理位置坐标 "经度,纬度"
 * @param {number} options.zoom 缩放级别 (1-18)
 * @param {string} options.size 地图尺寸 "宽度*高度"
 * @param {string} options.markers 标记点信息
 * @param {string} options.labels 标签信息
 * @returns {string} 静态地图URL
 */
export function generateAmapStaticUrl(options = {}) {
  const {
    location = '120.4551,30.5255', // 默认为嘉兴海宁坐标
    zoom = 15,
    size = '800*400',
    markers = '',
    labels = '',
    scale = 2, // 高清显示
    format = 'png'
  } = options

  const params = new URLSearchParams({
    location,
    zoom,
    size,
    scale,
    format,
    key: AMAP_CONFIG.key
  })

  // 添加标记点
  if (markers) {
    params.append('markers', markers)
  }

  // 添加标签
  if (labels) {
    params.append('labels', labels)
  }

  return `${AMAP_CONFIG.baseUrl}?${params.toString()}`
}

/**
 * 获取公司位置的高德静态地图URL
 * @param {string} size 地图尺寸，默认 "800*400"
 * @returns {string} 静态地图URL
 */
export function getCompanyMapUrl(size = '800*400') {
  // 公司地址：浙江省嘉兴市海宁市硖石街道水月亭东路500号鹃湖科技创新园14幢208室
  // 大概坐标位置（需要根据实际情况调整）
  const location = '120.4551,30.5255' // 海宁市硖石街道附近坐标

  const markers = `mid,0xFF0000,A:${location}` // 红色标记点
  const labels = `鹃湖科技创新园,2,0,16,0xFFFFFF,0x5288d8:${location}` // 标签

  return generateAmapStaticUrl({
    location,
    zoom: 16,
    size,
    markers,
    labels
  })
}

// 保留原有的百度地图函数以备后用
export function Bmap() {
  return new Promise(function (resolve, reject) {
    window.initBMapGL = function () {
      console.log('initBMapGL')
      resolve()
    }
    let script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = `//api.map.baidu.com/api?v=3.0&ak=jcI3Q88g6V99OPBjLryoOqPTtsRdedHA&callback=initBMapGL`

    script.onerror = reject

    document.head.appendChild(script)
  })
}

