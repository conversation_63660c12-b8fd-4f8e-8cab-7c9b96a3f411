<template>
  <div id="Contact">
    <!-- 联系我们背景图区域 -->
    <div class="contact-banner">
      <div class="banner-background">
        <img
          src="@/assets/img/contact/banner.jpg"
          alt="联系我们背景"
          class="banner-image"
        />
        <div class="banner-overlay"></div>
      </div>
      <div class="banner-content">
        <h1 class="wow fadeInUp">联系我们</h1>
        <p class="wow fadeInUp" data-wow-delay="0.2s">CONTACT US</p>
        <div class="title-divider wow fadeInUp" data-wow-delay="0.4s"></div>
      </div>
    </div>

    <div class="container">
      <div class="contact-container">
        <div class="row">
          <!-- 联系表单 -->
          <div class="col-xs-12 col-sm-12 col-md-6">
            <div class="contact-form wow slideInLeft">
              <h3>在线咨询</h3>
              <p>请填写以下信息，我们将尽快与您联系</p>
              <form
                class="form-horizontal"
                role="form"
                @submit.prevent="submitForm"
              >
                <div class="form-group">
                  <label for="name" class="col-sm-3 control-label"
                    >姓名 *</label
                  >
                  <div class="col-sm-9">
                    <input
                      type="text"
                      class="form-control"
                      id="name"
                      v-model="formData.name"
                      placeholder="请输入您的姓名"
                      required
                    />
                  </div>
                </div>
                <div class="form-group">
                  <label for="company" class="col-sm-3 control-label"
                    >公司</label
                  >
                  <div class="col-sm-9">
                    <input
                      type="text"
                      class="form-control"
                      id="company"
                      v-model="formData.company"
                      placeholder="请输入您的公司名称"
                    />
                  </div>
                </div>
                <div class="form-group">
                  <label for="phone" class="col-sm-3 control-label"
                    >电话 *</label
                  >
                  <div class="col-sm-9">
                    <input
                      type="tel"
                      class="form-control"
                      id="phone"
                      v-model="formData.phone"
                      placeholder="请输入您的联系电话"
                      required
                    />
                  </div>
                </div>
                <div class="form-group">
                  <label for="email" class="col-sm-3 control-label"
                    >邮箱 *</label
                  >
                  <div class="col-sm-9">
                    <input
                      type="email"
                      class="form-control"
                      id="email"
                      v-model="formData.email"
                      placeholder="请输入您的邮箱地址"
                      required
                    />
                  </div>
                </div>
                <div class="form-group">
                  <label for="interest" class="col-sm-3 control-label"
                    >感兴趣产品</label
                  >
                  <div class="col-sm-9">
                    <select
                      class="form-control"
                      id="interest"
                      v-model="formData.interest"
                    >
                      <option value="">请选择</option>
                      <option value="api">大模型API</option>
                      <option value="imaging">医疗影像工具</option>
                      <option value="platform">模型训练平台</option>
                      <option value="solution">整体解决方案</option>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label for="message" class="col-sm-3 control-label"
                    >留言</label
                  >
                  <div class="col-sm-9">
                    <textarea
                      class="form-control"
                      id="message"
                      rows="4"
                      v-model="formData.message"
                      placeholder="请描述您的需求或问题"
                    ></textarea>
                  </div>
                </div>
                <div class="form-group">
                  <div class="col-sm-offset-3 col-sm-9">
                    <button type="submit" class="btn btn-primary btn-lg">
                      提交咨询
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="col-xs-12 col-sm-12 col-md-6">
            <div class="contact-info wow slideInRight">
              <h3>联系方式</h3>
              <div class="contact-item">
                <img
                  src="@/assets/icons/address.png"
                  alt="地址"
                  class="contact-icon"
                />
                <div class="contact-details">
                  <h4>公司地址</h4>
                  <p>
                    浙江省嘉兴市海宁市硖石街道<br />水月亭东路500号鹃湖科技创新园14幢208室
                  </p>
                </div>
              </div>
              <div class="contact-item">
                <img
                  src="@/assets/icons/phone.png"
                  alt="电话"
                  class="contact-icon"
                />
                <div class="contact-details">
                  <h4>联系电话</h4>
                  <p>18633700697</p>
                </div>
              </div>
              <div class="contact-item">
                <img
                  src="@/assets/icons/service.png"
                  alt="邮箱"
                  class="contact-icon"
                />
                <div class="contact-details">
                  <h4>邮箱地址</h4>
                  <p><EMAIL></p>
                </div>
              </div>
              <div class="contact-item">
                <img
                  src="@/assets/icons/time.png"
                  alt="时间"
                  class="contact-icon"
                />
                <div class="contact-details">
                  <h4>工作时间</h4>
                  <p>周一至周五：9:00-18:00<br />周六：9:00-12:00</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-section">
      <div class="map-container wow fadeInUp" data-wow-delay="0.2s">
        <div class="amap-static">
          <img
            :src="mapUrl"
            alt="公司位置地图"
            class="static-map-img"
            @error="handleMapError"
          />
          <div class="map-overlay">
            <div class="location-info">
              <h4>鹃湖科技创新园</h4>
              <p>
                浙江省嘉兴市海宁市硖石街道<br />水月亭东路500号鹃湖科技创新园14幢208室
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Contact">
import { ref, onMounted, computed } from 'vue'
import WOW from 'wow.js'
import { getCompanyMapUrl } from '@/common/load-bmap.js'

const formData = ref({
  name: '',
  company: '',
  phone: '',
  email: '',
  interest: '',
  message: ''
})

// 计算地图URL
const mapUrl = computed(() => {
  // 根据屏幕尺寸动态调整地图大小，让地图占据全宽
  const width =
    window.innerWidth > 768
      ? Math.max(window.innerWidth, 1200)
      : window.innerWidth
  const height = window.innerWidth > 768 ? 500 : 350
  return getCompanyMapUrl(`${width}*${height}`)
})

const submitForm = () => {
  // 这里可以添加表单提交逻辑
  alert('感谢您的咨询，我们将尽快与您联系！')
  // 重置表单
  formData.value = {
    name: '',
    company: '',
    phone: '',
    email: '',
    interest: '',
    message: ''
  }
}

// 处理地图加载错误
const handleMapError = (event) => {
  console.error('地图加载失败:', event)
  event.target.style.display = 'none'
  // 可以显示一个备用的地址信息
}

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#Contact {
  padding: 0;
}

/* 联系我们背景图区域 */
.contact-banner {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.banner-content {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  padding: 0 20px;
}

.banner-content h1 {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-content p {
  font-size: 1.5rem;
  margin-bottom: 30px;
  opacity: 0.9;
  letter-spacing: 2px;
}

.title-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #1e3d72);
  margin: 0 auto;
  border-radius: 2px;
}

.banner {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 80px 0;
  font-size: 3rem;
  font-weight: bold;
}

.contact-container {
  padding: 80px 0;
}

.contact-form {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  color: #2c5aa0;
  margin-bottom: 10px;
  font-size: 2rem;
}

.contact-form p {
  color: #666;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 25px;
}

.control-label {
  color: #333;
  font-weight: 500;
  padding-top: 7px;
}

.form-control {
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 7px 15px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #2c5aa0;
  box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.btn-primary {
  background-color: #2c5aa0;
  border-color: #2c5aa0;
  padding: 12px 30px;
  font-size: 16px;
  border-radius: 8px;
}

.btn-primary:hover {
  background-color: #1e3d72;
  border-color: #1e3d72;
}

.contact-info {
  padding: 40px 20px;
}

.contact-info h3 {
  color: #2c5aa0;
  margin-bottom: 30px;
  font-size: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.contact-item i {
  font-size: 2rem;
  color: #2c5aa0;
  margin-right: 20px;
  margin-top: 5px;
  flex-shrink: 0;
}

.contact-icon {
  width: 32px;
  height: 32px;
  margin-right: 20px;
  margin-top: 5px;
  flex-shrink: 0;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(1234%)
    hue-rotate(201deg) brightness(94%) contrast(90%);
}

.contact-details h4 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1.2rem;
}

.contact-details p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.qr-codes {
  margin-top: 40px;
  text-align: center;
}

.qr-codes h4 {
  color: #333;
  margin-bottom: 20px;
}

.qr-grid {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.qr-item {
  text-align: center;
}

.qr-item img {
  width: 100px;
  height: 100px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.qr-item p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.map-section {
  padding: 0;
  background: #f8f9fa;
}

.section-title {
  text-align: center;
  color: #333;
  font-size: 2.5rem;
  margin-bottom: 50px;
}

.map-container {
  width: 100%;
  margin: 0;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  position: relative;
}

.amap-static {
  position: relative;
  width: 100%;
  height: 500px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.static-map-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.map-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.map-container:hover .map-overlay {
  transform: translateY(0);
}

.location-info h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.location-info p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .contact-banner {
    height: 70vh;
    min-height: 500px;
  }

  .banner-content h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
  }

  .banner-content p {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .title-divider {
    width: 60px;
    height: 3px;
  }

  .banner {
    font-size: 2rem;
    padding: 50px 0;
  }

  .contact-form,
  .contact-info {
    padding: 30px 20px;
    margin-bottom: 30px;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
  }

  .contact-item i {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .qr-grid {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .form-horizontal .control-label {
    text-align: left;
    margin-bottom: 5px;
  }

  .form-horizontal .col-sm-offset-3 {
    margin-left: 0;
  }

  .map-container {
    margin: 0;
  }

  .amap-static {
    height: 350px;
  }

  .map-overlay {
    position: static;
    transform: none;
    background: rgba(0, 0, 0, 0.8);
    margin-top: -60px;
    z-index: 1;
  }
}
</style>
