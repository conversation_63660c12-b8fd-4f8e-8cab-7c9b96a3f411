<template>
  <div id="SolutionDetail" class="container">
    <div class="solution-detail-content">
      <div class="solution-header wow fadeInUp">
        <h1>{{ currentSolution.title }}</h1>
        <p class="solution-subtitle">{{ currentSolution.subtitle }}</p>
      </div>

      <div class="solution-overview wow fadeInUp" data-wow-delay="0.2s">
        <div class="row">
          <div class="col-md-6">
            <img :src="currentSolution.image" :alt="currentSolution.title" class="img-responsive" />
          </div>
          <div class="col-md-6">
            <h3>方案概述</h3>
            <p>{{ currentSolution.overview }}</p>
            <div class="solution-features">
              <h4>核心特性</h4>
              <ul>
                <li v-for="feature in currentSolution.features" :key="feature">{{ feature }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="solution-benefits wow fadeInUp" data-wow-delay="0.4s">
        <h3>方案优势</h3>
        <div class="row">
          <div class="col-md-4" v-for="benefit in currentSolution.benefits" :key="benefit.title">
            <div class="benefit-card">
              <i :class="benefit.icon"></i>
              <h4>{{ benefit.title }}</h4>
              <p>{{ benefit.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="solution-workflow wow fadeInUp" data-wow-delay="0.6s">
        <h3>实施流程</h3>
        <div class="workflow-steps">
          <div class="step" v-for="(step, index) in currentSolution.workflow" :key="index">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h4>{{ step.title }}</h4>
              <p>{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="solution-cta wow fadeInUp" data-wow-delay="0.8s">
        <div class="cta-content">
          <h3>了解更多详情</h3>
          <p>联系我们的专家团队，获取定制化解决方案</p>
          <button class="btn btn-primary btn-lg">立即咨询</button>
          <button class="btn btn-outline-primary btn-lg">下载方案书</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SolutionDetail">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import WOW from 'wow.js'
import solution1 from '@/assets/img/service1.jpg'
import solution2 from '@/assets/img/service2.jpg'
import solution3 from '@/assets/img/service3.jpg'

const router = useRouter()

const solutions = {
  'hospital-system': {
    title: '医院辅助诊断系统',
    subtitle: '基于AI的智能医疗诊断解决方案',
    image: solution1,
    overview: '我们的医院辅助诊断系统集成了先进的医疗AI技术，为医院提供全面的智能诊断支持。系统能够快速准确地分析医学影像，辅助医生进行疾病诊断，提高诊断效率和准确性。',
    features: [
      '多模态医学影像分析',
      '实时诊断结果输出',
      '与PACS系统无缝集成',
      '支持多科室应用',
      '智能报告生成'
    ],
    benefits: [
      {
        icon: 'fas fa-clock',
        title: '提升效率',
        description: '诊断时间缩短60%，大幅提升医生工作效率'
      },
      {
        icon: 'fas fa-bullseye',
        title: '提高准确性',
        description: 'AI辅助诊断准确率达95%以上，减少误诊风险'
      },
      {
        icon: 'fas fa-users',
        title: '减轻负担',
        description: '自动化处理常规检查，让医生专注复杂病例'
      }
    ],
    workflow: [
      {
        title: '需求分析',
        description: '深入了解医院现有系统和诊断需求'
      },
      {
        title: '系统定制',
        description: '根据医院特点定制AI诊断模型'
      },
      {
        title: '系统集成',
        description: '与医院现有PACS和HIS系统集成'
      },
      {
        title: '培训上线',
        description: '医护人员培训和系统正式上线'
      }
    ]
  },
  'research-platform': {
    title: '医学科研平台',
    subtitle: '专业的医学AI研究和开发平台',
    image: solution2,
    overview: '为医学研究机构和科研人员提供专业的AI研究平台，支持大规模医学数据处理、模型训练和验证，加速医学AI技术的研发和应用。',
    features: [
      '大规模数据处理能力',
      '多种AI算法支持',
      '可视化研究工具',
      '协作研究环境',
      '成果发布平台'
    ],
    benefits: [
      {
        icon: 'fas fa-rocket',
        title: '加速研发',
        description: '提供强大的计算资源，加速AI模型研发进程'
      },
      {
        icon: 'fas fa-database',
        title: '数据管理',
        description: '统一的数据管理平台，支持多源数据整合'
      },
      {
        icon: 'fas fa-share-alt',
        title: '协作共享',
        description: '支持多机构协作研究和成果共享'
      }
    ],
    workflow: [
      {
        title: '平台部署',
        description: '根据研究需求部署专属研究平台'
      },
      {
        title: '数据接入',
        description: '整合多源医学数据，建立研究数据库'
      },
      {
        title: '模型开发',
        description: '使用平台工具进行AI模型开发和训练'
      },
      {
        title: '成果转化',
        description: '研究成果产业化和临床应用转化'
      }
    ]
  },
  'workflow-optimization': {
    title: '医生工作流优化',
    subtitle: '智能化医疗工作流程管理系统',
    image: solution3,
    overview: '通过AI技术优化医生的日常工作流程，自动化处理重复性任务，智能排班和资源调配，让医生能够更专注于患者诊疗工作。',
    features: [
      '智能排班系统',
      '自动化报告生成',
      '患者管理优化',
      '资源调配智能化',
      '工作效率分析'
    ],
    benefits: [
      {
        icon: 'fas fa-chart-line',
        title: '效率提升',
        description: '工作效率提升40%，减少重复性工作'
      },
      {
        icon: 'fas fa-balance-scale',
        title: '负载均衡',
        description: '智能分配工作负载，避免医生过度疲劳'
      },
      {
        icon: 'fas fa-heart',
        title: '关注患者',
        description: '释放更多时间用于患者沟通和诊疗'
      }
    ],
    workflow: [
      {
        title: '流程分析',
        description: '分析现有医疗工作流程，识别优化点'
      },
      {
        title: '系统设计',
        description: '设计智能化工作流程管理系统'
      },
      {
        title: '系统实施',
        description: '部署系统并进行流程优化改造'
      },
      {
        title: '效果评估',
        description: '持续监控和优化工作流程效果'
      }
    ]
  }
}

const currentSolution = ref(solutions['hospital-system'])

onMounted(() => {
  // 根据路由参数获取解决方案ID
  const solutionId = router.currentRoute.value.state?.id || 'hospital-system'
  currentSolution.value = solutions[solutionId] || solutions['hospital-system']

  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#SolutionDetail {
  padding: 130px 0 50px 0;
}

.solution-header {
  text-align: center;
  margin-bottom: 50px;
}

.solution-header h1 {
  color: #2c5aa0;
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.solution-subtitle {
  font-size: 1.2rem;
  color: #666;
}

.solution-overview {
  margin-bottom: 50px;
}

.solution-overview img {
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.solution-overview h3 {
  color: #333;
  margin-bottom: 20px;
}

.solution-features {
  margin-top: 30px;
}

.solution-features h4 {
  color: #2c5aa0;
  margin-bottom: 15px;
}

.solution-features ul {
  list-style: none;
  padding: 0;
}

.solution-features li {
  padding: 8px 0;
  position: relative;
  padding-left: 25px;
}

.solution-features li:before {
  content: "✓";
  color: #2c5aa0;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.solution-benefits {
  margin-bottom: 50px;
}

.solution-benefits h3 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.benefit-card {
  text-align: center;
  padding: 30px 20px;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 20px;
  transition: transform 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-5px);
}

.benefit-card i {
  font-size: 3rem;
  color: #2c5aa0;
  margin-bottom: 20px;
}

.benefit-card h4 {
  color: #333;
  margin-bottom: 15px;
}

.benefit-card p {
  color: #666;
  line-height: 1.6;
}

.solution-workflow {
  margin-bottom: 50px;
}

.solution-workflow h3 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.workflow-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}

.step {
  flex: 1;
  text-align: center;
  margin: 0 10px;
  min-width: 200px;
}

.step-number {
  width: 60px;
  height: 60px;
  background: #2c5aa0;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 20px;
}

.step-content h4 {
  color: #333;
  margin-bottom: 10px;
}

.step-content p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.solution-cta {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 60px 40px;
  text-align: center;
  border-radius: 10px;
}

.cta-content h3 {
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.cta-content .btn {
  margin: 0 10px;
  padding: 12px 30px;
}

.btn-outline-primary {
  border-color: white;
  color: white;
}

.btn-outline-primary:hover {
  background-color: white;
  color: #2c5aa0;
}

@media (max-width: 768px) {
  .workflow-steps {
    flex-direction: column;
  }

  .step {
    margin-bottom: 30px;
  }

  .solution-header h1 {
    font-size: 2rem;
  }

  .cta-content .btn {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}
</style>
