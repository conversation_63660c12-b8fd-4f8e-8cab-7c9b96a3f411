<template>
  <!-- 头部整体盒子 -->
  <div
    id="header"
    class="header-container"
    :class="{
      scrolled: isScrolled,
      'about-active': isAboutActive,
      'development-hidden': isDevelopmentSectionVisible,
      'about-page-hidden': isAboutPageHidden
    }"
  >
    <!-- 电脑导航 -->
    <div class="header-nav header-nav-container hidden-xs">
      <!-- 导航logo -->
      <div class="header-nav-logo">
        <img src="@/assets/img/top.png" alt="医疗超声AI" />
      </div>
      <!-- 导航内容 -->
      <nav class="header-nav-wrapper">
        <ul class="nav-list">
          <li
            v-for="(item, index) in navList"
            :key="index"
            :class="['nav-item', { active: index == navIndex }]"
            @click="navClick(index, item.name)"
          >
            <router-link :to="item.path" class="nav-link">
              <span class="nav-text">{{ item.name }}</span>
              <i v-if="item.children.length > 0" class="dropdown-icon"></i>
              <div class="nav-indicator"></div>
            </router-link>
            <!-- 下拉菜单 -->
            <div v-if="item.children.length > 0" class="dropdown-menu">
              <div class="dropdown-content">
                <router-link
                  v-for="(child, childIndex) in item.children"
                  :key="childIndex"
                  :to="child.path"
                  class="dropdown-item"
                >
                  <span>{{ child.name }}</span>
                </router-link>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </div>

    <!-- 手机导航 -->
    <div class="header-nav-mobile visible-xs">
      <div class="mobile-header">
        <div class="mobile-logo">
          <img src="@/assets/img/top.png" alt="医疗超声AI" />
        </div>
        <button class="mobile-menu-btn" @click="toggleMobileMenu">
          <span
            class="hamburger-line"
            :class="{ active: mobileMenuOpen }"
          ></span>
          <span
            class="hamburger-line"
            :class="{ active: mobileMenuOpen }"
          ></span>
          <span
            class="hamburger-line"
            :class="{ active: mobileMenuOpen }"
          ></span>
        </button>
      </div>

      <!-- 移动端菜单 -->
      <div class="mobile-menu" :class="{ open: mobileMenuOpen }">
        <nav class="mobile-nav">
          <ul class="mobile-nav-list">
            <li
              v-for="(item, index) in navList"
              :key="index"
              class="mobile-nav-item"
              :class="{ active: index == navIndex }"
            >
              <router-link
                :to="item.path"
                class="mobile-nav-link"
                @click="navClick(index, item.name), closeMobileMenu()"
              >
                {{ item.name }}
              </router-link>
              <!-- 移动端子菜单 -->
              <ul v-if="item.children.length > 0" class="mobile-submenu">
                <li
                  v-for="(child, childIndex) in item.children"
                  :key="childIndex"
                  class="mobile-submenu-item"
                >
                  <router-link
                    :to="child.path"
                    class="mobile-submenu-link"
                    @click="closeMobileMenu()"
                  >
                    {{ child.name }}
                  </router-link>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const navIndex = ref(0)
const mobileMenuOpen = ref(false)
const isScrolled = ref(false)
const isDevelopmentSectionVisible = ref(false)
const isAboutPageHidden = ref(false)

// 计算是否选中了"关于我们"
const isAboutActive = computed(() => {
  return navIndex.value === 5 // "关于我们"是第6个项目，索引为5
})

// 初始化导航索引
navIndex.value = sessionStorage.getItem('navIndex')
  ? parseInt(sessionStorage.getItem('navIndex'))
  : 0

// 更新导航列表以匹配医疗超声AI网站
const navList = [
  {
    name: '首页',
    path: '/home',
    children: []
  },
  {
    name: '产品中心',
    path: '/products',
    children: []
  },
  {
    name: '解决方案',
    path: '/solutions',
    children: []
  },
  {
    name: '成功案例',
    path: '/cases',
    children: []
  },
  {
    name: '新闻资讯',
    path: '/news',
    children: []
  },
  {
    name: '关于我们',
    path: '/about',
    children: []
  },

  {
    name: '联系我们',
    path: '/contact',
    children: []
  }
]

// 导航点击事件
function navClick(index, name) {
  navIndex.value = index
  sessionStorage.setItem('navIndex', index)
}

// 切换移动端菜单
function toggleMobileMenu() {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// 关闭移动端菜单
function closeMobileMenu() {
  mobileMenuOpen.value = false
}

// 监听窗口大小变化，大屏幕时自动关闭移动菜单
function handleResize() {
  if (window.innerWidth > 768) {
    mobileMenuOpen.value = false
  }
}

// 监听滚动事件
function handleScroll() {
  isScrolled.value = window.scrollY > 50

  // 检查是否在关于我们页面
  const isAboutPage = route.path === '/about'

  if (isAboutPage) {
    // 在关于我们页面，只有在最顶部时显示导航栏
    isAboutPageHidden.value = window.scrollY > 10
  } else {
    // 其他页面保持正常显示
    isAboutPageHidden.value = false
  }
}

// 监听发展历程区域可见性
function handleDevelopmentSectionVisibility(event) {
  isDevelopmentSectionVisible.value = event.detail.isVisible
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll)
  window.addEventListener(
    'developmentSectionVisibility',
    handleDevelopmentSectionVisibility
  )
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener(
    'developmentSectionVisibility',
    handleDevelopmentSectionVisibility
  )
})
</script>

<style scoped>
/* 现代化医疗主题导航栏样式 */

/* 头部容器 */
.header-container {
  background: transparent;
  box-shadow: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* 滚动时的样式 */
.header-container.scrolled {
  background: rgba(252, 251, 251, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

/* 发展历程区域时隐藏导航栏 */
.header-container.development-hidden {
  transform: translateY(-100%);
  transition: transform 0.5s ease-in-out;
  pointer-events: none;
}

/* 关于我们页面滚动时隐藏导航栏 */
.header-container.about-page-hidden {
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  pointer-events: none;
}

/* 桌面端导航 */
.header-nav {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0; /* 移除padding，使用margin控制间距 */
  max-width: none;
  margin: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100%;
  box-sizing: border-box;
}

/* 导航容器样式，覆盖全局容器样式 */
.header-nav-container {
  margin: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  max-width: none !important;
  width: 100% !important;
}

/* Logo区域 */
.header-nav-logo {
  display: flex;
  align-items: center;
  margin-left: 6rem; /* logo前面留很多距离 */
}

.header-nav-logo img {
  width: 180px;
  height: 80px;
  border-radius: 8px;
  transition: transform 0.3s ease;
  object-fit: contain;
}

.header-nav-logo img:hover {
  transform: scale(1.05);
}

/* 导航容器 */
.header-nav-wrapper {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.nav-list {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style: none;
  gap: 5px;
  margin-right: 4rem; /* 导航菜单后面留更多距离 */
}

/* 导航项 */
.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.9); /* 初始状态为白色 */
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* 添加文字阴影增强可读性 */
}

.nav-text {
  position: relative;
  z-index: 2;
}

/* 导航指示器 */
.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 1)
  );
  border-radius: 2px;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

/* 下拉图标 */
.dropdown-icon {
  margin-left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  transition: transform 0.3s ease;
}

/* 悬停效果 */
.nav-link:hover {
  color: rgba(255, 255, 255, 1);
  background: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.nav-link:hover .nav-indicator {
  width: 100%;
}

.nav-link:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 激活状态 */
.nav-item.active .nav-link {
  color: rgba(255, 255, 255, 1); /* 初始状态激活项也是白色 */
  font-weight: 600;
  /* 移除背景色 */
}

.nav-item.active .nav-indicator {
  width: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 1)
  ); /* 初始状态指示器为白色 */
}

/* 当选中"关于我们"时，所有导航项都变为黑色 */
.header-container.about-active .nav-link {
  color: rgba(0, 0, 0, 0.9) !important;
  text-shadow: none !important;
}

.header-container.about-active .nav-item.active .nav-indicator {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.8),
    rgba(0, 0, 0, 1)
  ) !important;
}

/* 滚动状态下的导航样式 */
.header-container.scrolled .nav-link {
  color: rgba(7, 7, 7, 1); /* 滚动后文字变为黑色 */
  text-shadow: none; /* 移除文字阴影 */
}

.header-container.scrolled .nav-link:hover {
  color: rgba(59, 130, 246, 1); /* 滚动后悬停颜色为蓝色 */
  background: rgba(59, 130, 246, 0.1);
}

.header-container.scrolled .nav-item.active .nav-link {
  color: rgba(0, 0, 0, 0.9); /* 滚动后激活项保持黑色 */
}

.header-container.scrolled .nav-item.active .nav-indicator {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.8),
    rgba(0, 0, 0, 1)
  ); /* 滚动后指示器保持黑色 */
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 220px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-item:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-content {
  padding: 8px 0;
}

.dropdown-item {
  display: block;
  padding: 12px 20px;
  color: #374151;
  font-size: 14px;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.dropdown-item:hover {
  background: rgba(59, 130, 246, 0.05);
  color: #2563eb;
  border-left-color: #2563eb;
  text-decoration: none;
}

/* 移动端导航 */
.header-nav-mobile {
  display: none;
}

.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0; /* 移除左右padding，使用margin控制间距 */
  background: transparent;
}

.mobile-logo {
  display: flex;
  align-items: center;
  margin-left: 3rem; /* 移动端logo前面留很多距离 */
}

.mobile-logo img {
  width: 140px;
  height: 70px;
  border-radius: 6px;
  object-fit: contain;
}

/* 汉堡菜单按钮 */
.mobile-menu-btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-right: 3rem; /* 移动端菜单按钮后面留很多距离 */
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background: rgba(255, 255, 255, 0.9); /* 初始状态为白色 */
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

/* 滚动状态下的移动端汉堡菜单样式 */
.header-container.scrolled .hamburger-line {
  background: #374151; /* 滚动后变为深色 */
}

.hamburger-line.active:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.active:nth-child(2) {
  opacity: 0;
}

.hamburger-line.active:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 移动端菜单 */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
}

.mobile-menu.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 20px 0;
}

.mobile-nav-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-nav-link {
  display: block;
  padding: 15px 20px;
  color: #374151;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.mobile-nav-link:hover {
  color: #2563eb;
  background: rgba(59, 130, 246, 0.05);
  text-decoration: none;
}

.mobile-nav-item.active .mobile-nav-link {
  color: #2563eb;
  /* 移除激活状态的背景色 */
  text-decoration: none;
}

.mobile-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(0, 0, 0, 0.02);
}

.mobile-submenu-link {
  display: block;
  padding: 12px 40px;
  color: #6b7280;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.mobile-submenu-link:hover {
  color: #2563eb;
  background: rgba(59, 130, 246, 0.05);
  text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-nav {
    display: none;
  }

  .header-nav-mobile {
    display: block;
  }

  .mobile-header {
    padding: 15px 0; /* 小屏幕时移除padding，使用margin控制间距 */
  }

  .mobile-logo {
    margin-left: 2rem; /* 小屏幕logo前面距离增加 */
  }

  .mobile-menu-btn {
    margin-right: 2rem; /* 小屏幕菜单按钮后面距离增加 */
  }
}

@media (min-width: 769px) {
  .header-nav {
    display: flex;
  }

  .header-nav-mobile {
    display: none;
  }
}

/* 大屏幕时增加更多边距 */
@media (min-width: 1200px) {
  .header-nav {
    padding: 0; /* 移除padding，使用margin控制间距 */
  }

  .header-nav-logo {
    margin-left: 8rem; /* 大屏幕logo前面距离更多 */
  }

  .nav-list {
    margin-right: 8rem; /* 大屏幕导航菜单后面距离更多 */
  }
}

/* 中等屏幕调整 */
@media (max-width: 1024px) and (min-width: 769px) {
  .header-nav {
    padding: 0; /* 移除padding，使用margin控制间距 */
  }

  .header-nav-logo {
    margin-left: 4rem; /* 中等屏幕logo前面距离增加 */
  }

  .nav-list {
    margin-right: 4rem; /* 中等屏幕导航菜单后面距离增加 */
  }
}
</style>

