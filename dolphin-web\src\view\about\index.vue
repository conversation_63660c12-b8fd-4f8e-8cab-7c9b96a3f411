<template>
  <div id="About">
    <!-- <div class="banner container-fuild text-center">关于我们</div> -->
    <div class="container">
      <!-- 公司简介 -->
      <div class="company-profile-section">
        <div class="profile-content">
          <div class="profile-text">
            <div class="profile-label">COMPANY PROFILE</div>
            <h2 class="profile-title-cn">科技助力健康</h2>
            <h1 class="profile-title-en">TECHNOLOGY BOOSTS HEALTH</h1>
            <div class="profile-description">
              <p>
                海宁海豚之声医疗科技有限公司成立于2024年8月20日，注册资本200万元，由法定代表人郑浩云领导。我们是一家专注于医疗人工智能技术研发的高科技企业，致力于将先进的深度学习和大模型技术应用于医疗超声影像分析领域。公司成立以来，始终坚持技术创新，以"让AI赋能医疗，让诊断更精准"为使命，为医疗机构提供专业的AI解决方案。
              </p>
              <p>
                公司位于浙江省嘉兴市海宁市硖石街道水月亭东路500号鹃湖科技创新园14幢208室，拥有一支由医学专家、AI算法工程师和产品经理组成的专业团队，在医疗影像AI、深度学习算法、医疗大数据分析等领域具有丰富的经验和深厚的技术积累。我们的产品已在全国多家三甲医院成功应用，获得了医生和患者的广泛认可。
              </p>
            </div>
          </div>
          <div class="profile-image">
            <img src="@/assets/img/about/img1.jpg" alt="现代化建筑" />
            <div class="image-decoration"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 发展历程 - 全新横向设计 -->
    <div
      class="development-section"
      :class="{
        'has-hover': hoveredIndex >= 0
      }"
    >
      <div
        class="development-background"
        :style="{ backgroundImage: `url(${currentBackgroundImage})` }"
      >
        <div
          class="development-overlay"
          :class="{ 'preview-mode': hoveredIndex >= 0 }"
        >
          <div class="development-header">
            <div class="development-label">DEVELOPMENT PATH</div>
            <h2 class="development-title-cn">发展历程</h2>
            <h1 class="development-title-en">DOLPHIN JOURNEY</h1>
          </div>

          <div class="development-content" :key="currentTimelineIndex">
            <div class="development-info">
              <div class="development-date">{{ currentTimelineItem.date }}</div>
              <div class="development-details">
                <h3>{{ currentTimelineItem.title }}</h3>
                <p>{{ currentTimelineItem.description }}</p>
                <div class="development-extra">
                  {{ currentTimelineItem.extra }}
                </div>
              </div>
            </div>

            <div
              class="development-image"
              v-if="currentTimelineItem.contentImage"
            >
              <img
                :src="currentTimelineItem.contentImage"
                :alt="currentTimelineItem.title"
              />
            </div>
          </div>

          <!-- 底部时间轴导航 -->
          <div class="timeline-navigation">
            <div class="timeline-track">
              <div
                class="timeline-progress"
                :style="{ width: progressWidth }"
              ></div>
              <div
                class="timeline-point"
                v-for="(item, index) in timelineData"
                :key="index"
                :class="{
                  active: currentTimelineIndex === index,
                  hovered: hoveredIndex === index
                }"
                :style="{
                  left: `${(index / (timelineData.length - 1)) * 100}%`
                }"
                @click="selectTimelineItem(index)"
                @mouseenter="handleMouseEnter(index)"
                @mouseleave="handleMouseLeave"
              >
                <div class="timeline-point-inner"></div>
                <div class="timeline-date-label">{{ item.date }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资质荣誉 -->
    <div class="honors-section">
      <div class="honors-container">
        <h3 class="section-title wow fadeInUp">荣誉资质</h3>
        <p class="section-subtitle wow fadeInUp">HONOR QUALIFICATION</p>
        <div class="honors-grid">
          <div
            class="honor-card wow zoomIn"
            v-for="honor in honors.slice(0, 4)"
            :key="honor.title"
            :data-wow-delay="`${honors.slice(0, 4).indexOf(honor) * 0.1}s`"
          >
            <div class="honor-image">
              <img
                :src="honor.image"
                :alt="honor.title"
                class="honor-certificate"
              />
            </div>
            <div class="honor-info">
              <h4>{{ honor.title }}</h4>
              <p>{{ honor.description }}</p>
            </div>
          </div>
        </div>
        <!-- 装饰分割线 -->
        <div class="honors-divider">
          <div class="divider-line"></div>
          <div class="divider-dot"></div>
          <div class="divider-line"></div>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- 团队介绍 -->
      <div class="team-section">
        <h3 class="section-title wow fadeInUp">核心团队</h3>
        <p class="section-subtitle wow fadeInUp">CORE TEAM</p>
        <div class="team-grid">
          <div
            class="team-member-card wow slideInUp"
            v-for="member in teamMembers"
            :key="member.name"
          >
            <img
              src="https://uiverse.io/astronaut.png"
              alt=""
              class="member-image"
            />
            <div class="member-heading">{{ member.name }}</div>
            <div class="member-position-badge">{{ member.position }}</div>
            <p class="member-description">{{ member.description }}</p>
            <div class="member-icons">
              <a href="#" class="social-icon instagram">
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.0459 7.5H17.0559M3.0459 12.5C3.0459 9.986 3.0459 8.73 3.3999 7.72C3.71249 6.82657 4.22237 6.01507 4.89167 5.34577C5.56096 4.67647 6.37247 4.16659 7.2659 3.854C8.2759 3.5 9.5329 3.5 12.0459 3.5C14.5599 3.5 15.8159 3.5 16.8269 3.854C17.7202 4.16648 18.5317 4.67621 19.201 5.34533C19.8702 6.01445 20.3802 6.82576 20.6929 7.719C21.0459 8.729 21.0459 9.986 21.0459 12.5C21.0459 15.014 21.0459 16.27 20.6929 17.28C20.3803 18.1734 19.8704 18.9849 19.2011 19.6542C18.5318 20.3235 17.7203 20.8334 16.8269 21.146C15.8169 21.5 14.5599 21.5 12.0469 21.5C9.5329 21.5 8.2759 21.5 7.2659 21.146C6.37268 20.8336 5.56131 20.324 4.89202 19.6551C4.22274 18.9862 3.71274 18.1751 3.3999 17.282C3.0459 16.272 3.0459 15.015 3.0459 12.501V12.5ZM15.8239 11.94C15.9033 12.4387 15.8829 12.9481 15.7641 13.4389C15.6453 13.9296 15.4304 14.392 15.1317 14.7991C14.833 15.2063 14.4566 15.5501 14.0242 15.8108C13.5917 16.0715 13.1119 16.2439 12.6124 16.318C12.1129 16.392 11.6037 16.3663 11.1142 16.2422C10.6248 16.1182 10.1648 15.8983 9.76082 15.5953C9.35688 15.2923 9.01703 14.9123 8.76095 14.4771C8.50486 14.0419 8.33762 13.5602 8.2689 13.06C8.13201 12.0635 8.39375 11.0533 8.99727 10.2487C9.6008 9.44407 10.4974 8.91002 11.4923 8.76252C12.4873 8.61503 13.5002 8.86599 14.3112 9.46091C15.1222 10.0558 15.6658 10.9467 15.8239 11.94Z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></path>
                </svg>
              </a>
              <a href="#" class="social-icon x">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M19.8003 3L13.5823 10.105L19.9583 19.106C20.3923 19.719 20.6083 20.025 20.5983 20.28C20.594 20.3896 20.5657 20.4969 20.5154 20.5943C20.4651 20.6917 20.3941 20.777 20.3073 20.844C20.1043 21 19.7293 21 18.9793 21H17.2903C16.8353 21 16.6083 21 16.4003 20.939C16.2168 20.8847 16.0454 20.7957 15.8953 20.677C15.7253 20.544 15.5943 20.358 15.3313 19.987L10.6813 13.421L4.64033 4.894C4.20733 4.281 3.99033 3.975 4.00033 3.72C4.00478 3.61035 4.03323 3.50302 4.08368 3.40557C4.13414 3.30812 4.20536 3.22292 4.29233 3.156C4.49433 3 4.87033 3 5.62033 3H7.30833C7.76333 3 7.99033 3 8.19733 3.061C8.38119 3.1152 8.55295 3.20414 8.70333 3.323C8.87333 3.457 9.00433 3.642 9.26733 4.013L13.5833 10.105M4.05033 21L10.6823 13.421"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></path>
                </svg>
              </a>
              <a href="#" class="social-icon discord">
                <svg
                  width="25"
                  height="25"
                  viewBox="0 0 25 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11.5989 6.5003H14.2919C14.3851 6.5003 14.4764 6.47427 14.5555 6.42515C14.6347 6.37603 14.6985 6.30577 14.7399 6.2223L15.4179 4.8543C15.4664 4.75358 15.5488 4.67313 15.6506 4.62706C15.7524 4.58098 15.8673 4.57222 15.9749 4.6023C16.6309 4.7903 18.0049 5.2433 19.1029 6.0003C22.9669 8.8973 22.6069 15.3903 22.5779 16.7603C22.5765 16.8444 22.5541 16.9269 22.5129 17.0003C20.5299 20.5003 17.0899 20.5003 17.0899 20.5003L15.9239 18.0743M15.9239 18.0743C16.4479 17.9163 17.0029 17.7253 17.6029 17.5003M15.9239 18.0743C13.4799 18.8093 11.7219 18.8083 9.27791 18.0733M13.5989 6.5003H10.9109C10.8179 6.50039 10.7266 6.47451 10.6475 6.42557C10.5683 6.37664 10.5044 6.30659 10.4629 6.2233L9.77991 4.8533C9.73146 4.75279 9.64925 4.6725 9.54762 4.62644C9.446 4.58038 9.33142 4.57148 9.22391 4.6013C8.56891 4.7893 7.19291 5.2433 6.09391 6.0003C2.23091 8.8973 2.59091 15.3903 2.61991 16.7603C2.62132 16.8445 2.64366 16.9269 2.68491 17.0003C4.66791 20.5003 8.10791 20.5003 8.10791 20.5003L9.27791 18.0733M9.27791 18.0733C8.75491 17.9163 8.19891 17.7253 7.59891 17.5003M10.6009 12.5003C10.6009 12.7655 10.4956 13.0199 10.308 13.2074C10.1205 13.3949 9.86612 13.5003 9.60091 13.5003C9.33569 13.5003 9.08134 13.3949 8.8938 13.2074C8.70626 13.0199 8.60091 12.7655 8.60091 12.5003C8.60091 12.2351 8.70626 11.9807 8.8938 11.7932C9.08134 11.6057 9.33569 11.5003 9.60091 11.5003C9.86612 11.5003 10.1205 11.6057 10.308 11.7932C10.4956 11.9807 10.6009 12.2351 10.6009 12.5003ZM16.6029 12.5003C16.6029 12.7655 16.4976 13.0199 16.31 13.2074C16.1225 13.3949 15.8681 13.5003 15.6029 13.5003C15.3377 13.5003 15.0833 13.3949 14.8958 13.2074C14.7083 13.0199 14.6029 12.7655 14.6029 12.5003C14.6029 12.2351 14.7083 11.9807 14.8958 11.7932C15.0833 11.6057 15.3377 11.5003 15.6029 11.5003C15.8681 11.5003 16.1225 11.6057 16.31 11.7932C16.4976 11.9807 16.6029 12.2351 16.6029 12.5003Z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="About">
import { onMounted, ref, computed } from 'vue'
import WOW from 'wow.js'
const teamMembers = [
  {
    name: '勒安捷',
    position: '海豚智能创始人 & CEO',
    description:
      '95后科学家，海豚智能创立者。专注于医疗AI技术创新，致力于推动人工智能在医疗超声影像分析领域的应用与发展，具有敏锐的科技洞察力和卓越的领导能力。'
  },
  {
    name: '郑浩云',
    position: '海宁海豚之声医疗科技有限公司联合创始人',
    description:
      '五年软件工程师经验，负责公司技术架构设计和团队管理，在人工智能和机器学习领域具有深厚的技术积累。'
  },
  {
    name: '郭宏成',
    position: '联合创始人 & 首席技术官',
    description:
      '北京航空航天大学计算机学院博士，北航软件国家重点实验室成员。华为天才少年第一档、微软明日之星、腾讯青云计划、美团北斗计划获得者。曾在阿里、阶跃星辰、小红书等公司从事大模型开发，发表大模型相关CCF A/B类和SCI论文20余篇。'
  }
]

const honors = [
  {
    image: 'https://via.placeholder.com/400x500/f0f0f0/666?text=证书1',
    title: '实用新型专利证书',
    description: '获得国家知识产权局颁发的实用新型专利证书'
  },
  {
    image: 'https://via.placeholder.com/400x500/f0f0f0/666?text=证书2',
    title: '实用新型专利证书',
    description: '获得国家知识产权局颁发的实用新型专利证书'
  },
  {
    image: 'https://via.placeholder.com/400x500/f0f0f0/666?text=证书3',
    title: '实用新型专利证书',
    description: '获得国家知识产权局颁发的实用新型专利证书'
  },
  {
    image: 'https://via.placeholder.com/400x500/f0f0f0/666?text=证书4',
    title: '实用新型专利证书',
    description: '获得国家知识产权局颁发的实用新型专利证书'
  }
]

// 新的横向时间轴数据
const timelineData = [
  {
    date: '2024.06',
    title: '海豚智能团队成立',
    description: '海宁海豚之声有限公司正式成立，专注于医疗AI技术研发',
    extra: '海豚智能团队正式成立',
    backgroundImage: '/src/assets/img/devlop/banner.jpg',
    contentImage: '/src/assets/img/devlop/1127cb903f8db5463e92570bfb05a2ea.jpg'
  },
  {
    date: '2024.10',
    title: '入选"潮城英才"领军计划',
    description: '凭借优秀的技术实力和创新能力，成功入选"潮城英才"领军计划',
    extra: '获得政府人才计划认可',
    backgroundImage: '/src/assets/img/devlop/banner.jpg',
    contentImage: '/src/assets/img/devlop/1b1179912b7e890c1009f7d67e4823ad.jpg'
  },
  {
    date: '2025.03',
    title: '完成数百万元种子轮融资',
    description:
      '成功完成数百万元种子轮融资，为技术研发和市场拓展提供强有力支持',
    extra: '种子轮融资成功完成',
    backgroundImage: '/src/assets/img/devlop/banner.jpg',
    contentImage: '/src/assets/img/devlop/1127cb903f8db5463e92570bfb05a2ea.jpg'
  },
  {
    date: '2025.06',
    title: '开源全球首个超声AI大模型',
    description: '正式开源全球首个超声AI大模型，引领医疗AI技术发展新方向',
    extra: '技术创新里程碑达成',
    backgroundImage: '/src/assets/img/devlop/banner.jpg',
    contentImage: '/src/assets/img/devlop/1b1179912b7e890c1009f7d67e4823ad.jpg'
  }
]

// 当前选中的时间轴项目索引
const currentTimelineIndex = ref(0)
// 当前悬停的时间轴项目索引
const hoveredIndex = ref(-1)

// 当前显示的时间轴项目（优先显示悬停的，否则显示选中的）
const currentTimelineItem = computed(() => {
  const index =
    hoveredIndex.value >= 0 ? hoveredIndex.value : currentTimelineIndex.value
  return timelineData[index]
})

// 当前背景图片（带有悬停预览效果）
const currentBackgroundImage = computed(() => {
  if (hoveredIndex.value >= 0) {
    return timelineData[hoveredIndex.value].backgroundImage
  }
  return timelineData[currentTimelineIndex.value].backgroundImage
})

// 进度条宽度（悬停时跟随鼠标，否则跟随选中项）
const progressWidth = computed(() => {
  const index =
    hoveredIndex.value >= 0 ? hoveredIndex.value : currentTimelineIndex.value
  return `${(index / (timelineData.length - 1)) * 100}%`
})

// 选择时间轴项目
const selectTimelineItem = (index) => {
  if (currentTimelineIndex.value !== index) {
    currentTimelineIndex.value = index
  }
}

// 处理鼠标进入
const handleMouseEnter = (index) => {
  if (hoveredIndex.value !== index) {
    hoveredIndex.value = index
  }
}

// 处理鼠标离开 - 将悬停的位置设为当前选中位置
const handleMouseLeave = () => {
  if (hoveredIndex.value >= 0) {
    currentTimelineIndex.value = hoveredIndex.value
  }
  hoveredIndex.value = -1
}

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#About {
  padding: 80px 0 0 0;
}

.banner {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 80px 0;
  font-size: 2.5vw;
  font-weight: bold;
}

/* 公司简介部分样式 */
.company-profile-section {
  padding: 80px 0;
  background: #ffffff;
}

.profile-content {
  display: flex;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 60px;
}

.profile-text {
  flex: 0 0 50%;
  max-width: 535px;
  padding-right: 20px;
  z-index: 2;
  margin-left: -200px;
}

.profile-label {
  color: #2c5aa0;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.profile-title-cn {
  color: #333;
  font-size: 2.2rem;
  font-weight: 400;
  margin-bottom: 15px;
  line-height: 1.2;
}

.profile-title-en {
  color: #000;
  font-size: 4.5rem;
  font-weight: 600;
  line-height: 0.9;
  margin-bottom: 40px;
  letter-spacing: -2px;
  word-spacing: 8px;
  margin-left: 0px;
}

.profile-description {
  color: #555;
  line-height: 1.8;
}

.profile-description p {
  font-size: 1.7rem;
  margin-bottom: 25px;
  text-align: justify;
  line-height: 1.7;
}

.profile-image {
  flex: 0 0 50%;
  position: relative;
  margin-top: -20px;
}

.profile-image img {
  width: 192%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  margin-left: -50px;
  position: relative;
  z-index: 1;
}

.image-decoration {
  position: absolute;
  bottom: -15px;
  right: -15px;
  width: 120px;
  height: 30px;
  background: #2c5aa0;
  border-radius: 0 0 8px 0;
  z-index: 2;
}

.team-section {
  padding: 80px 0;
}

/* 团队部分标题样式 */
.team-section .section-title {
  font-size: 2.5vw;
  color: #2c5aa0;
  margin-bottom: 10px;
  text-align: center;
  font-weight: bold;
}

.team-section .section-subtitle {
  font-size: 4.2vw;
  color: #666;
  text-align: center;
  margin-bottom: 50px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* 通用标题样式（保留作为后备） */
.section-title {
  text-align: center;
  color: #333;
  font-size: 2.5rem;
  margin-bottom: 50px;
}

.team-grid {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: nowrap;
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: auto;
  padding: 0 20px;
}

/* 3D 卡片样式 */
.team-member-card {
  position: relative;
  width: 26em; /* 增加卡片宽度 */
  height: 32em; /* 增加卡片高度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #171717;
  color: white;
  font-family: Montserrat, sans-serif;
  font-weight: bold;
  padding: 1.5em 2.5em 1.5em 1.5em; /* 增加内边距 */
  border-radius: 20px;
  overflow: hidden;
  z-index: 1;
  row-gap: 1.5em; /* 增加元素间距 */
}

.team-member-card .member-image {
  width: 10.5em; /* 增加头像尺寸 */
  height: 10.5em;
  margin-right: 1em;
  animation: move 10s ease-in-out infinite;
  z-index: 5;
  border-radius: 50%;
  object-fit: cover;
}

.member-image:hover {
  cursor: -webkit-grab;
  cursor: grab;
}

.member-icons svg {
  width: 20px;
  height: 20px;
}

.team-member-card::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  inset: -3px;
  border-radius: 10px;
  background: radial-gradient(#858585, transparent, transparent);
  transform: translate(-5px, 250px);
  transition: 0.4s ease-in-out;
  z-index: -1;
}

.team-member-card:hover::before {
  width: 150%;
  height: 100%;
  margin-left: -4.25em;
}

.team-member-card::after {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 20px;
  background: rgb(23, 23, 23, 0.7);
  transition: all 0.4s ease-in-out;
  z-index: -1;
}

.member-heading {
  z-index: 2;
  transition: 0.4s ease-in-out;
  font-size: 2.2rem; /* 增大姓名文字 */
  font-weight: bold;
  color: white;
}

.team-member-card:hover .member-heading {
  letter-spacing: 0.025em;
}

.member-position-badge {
  background: #2c5aa0;
  color: white;
  padding: 10px 22px; /* 增加内边距 */
  border-radius: 15px;
  font-size: 1.2rem; /* 增大职位文字 */
  font-weight: 500;
  display: inline-block;
  margin-bottom: 15px; /* 增加底部间距 */
  z-index: 2;
}

.member-description {
  color: #ccc;
  font-size: 1.1rem; /* 增大描述文字 */
  line-height: 1.7; /* 增加行高 */
  margin: 0;
  text-align: center;
  font-weight: normal;
  z-index: 2;
  padding: 0 18px; /* 增加左右内边距 */
}

/* 星空背景效果 */
.member-heading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  opacity: 1;
  box-shadow: 220px 118px #fff, 280px 176px #fff, 40px 50px #fff,
    60px 180px #fff, 120px 130px #fff, 180px 176px #fff, 220px 290px #fff,
    520px 250px #fff, 400px 220px #fff, 50px 350px #fff, 10px 230px #fff;
  z-index: -1;
  transition: 1s ease;
  animation: 1s glowing-stars linear alternate infinite;
  animation-delay: 0s;
}

.member-icons::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  opacity: 1;
  box-shadow: 140px 20px #fff, 425px 20px #fff, 70px 120px #fff, 20px 130px #fff,
    110px 80px #fff, 280px 80px #fff, 250px 350px #fff, 280px 230px #fff,
    220px 190px #fff, 450px 100px #fff, 380px 80px #fff, 520px 50px #fff;
  z-index: -1;
  transition: 1.5s ease;
  animation: 1s glowing-stars linear alternate infinite;
  animation-delay: 0.4s;
}

.member-icons::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  opacity: 1;
  box-shadow: 490px 330px #fff, 420px 300px #fff, 320px 280px #fff,
    380px 350px #fff, 546px 170px #fff, 420px 180px #fff, 370px 150px #fff,
    200px 250px #fff, 80px 20px #fff, 190px 50px #fff, 270px 20px #fff,
    120px 230px #fff, 350px -1px #fff, 150px 369px #fff;
  z-index: -1;
  transition: 2s ease;
  animation: 1s glowing-stars linear alternate infinite;
  animation-delay: 0.8s;
}

.team-member-card:hover .member-heading::before,
.team-member-card:hover .member-icons::before,
.team-member-card:hover .member-icons::after {
  filter: blur(3px);
}

/* 光晕效果 */
.member-heading::after {
  content: '';
  top: -8.5%;
  left: -8.5%;
  position: absolute;
  width: 7.5em;
  height: 7.5em;
  border: none;
  outline: none;
  border-radius: 50%;
  background: #f9f9fb;
  box-shadow: 0px 0px 100px rgba(193, 119, 241, 0.8),
    0px 0px 100px rgba(135, 42, 211, 0.8), inset #9b40fc 0px 0px 40px -12px;
  transition: 0.4s ease-in-out;
  z-index: -1;
}

.team-member-card:hover .member-heading::after {
  box-shadow: 0px 0px 200px rgba(193, 119, 241, 1),
    0px 0px 200px rgba(135, 42, 211, 1), inset #9b40fc 0px 0px 40px -12px;
}

/* 社交图标样式 */
.member-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  column-gap: 1em;
  z-index: 1;
}

.social-icon {
  position: relative;
  transition: 0.4s ease-in-out;
  text-decoration: none;
}

.social-icon:after {
  content: '';
  position: absolute;
  width: 0.5em;
  height: 0.5em;
  left: 0;
  background-color: white;
  box-shadow: 0px 0px 10px rgba(233, 233, 233, 0.5),
    0px 0px 10px rgba(192, 192, 192, 0.5);
  border-radius: 50%;
  z-index: -1;
  transition: 0.3s ease-in-out;
}

.social-icon svg path {
  stroke: #808080;
  transition: 0.4s ease-in-out;
}

.instagram:hover svg path {
  stroke: #cc39a4;
}

.x:hover svg path {
  stroke: black;
}

.discord:hover svg path {
  stroke: #8c9eff;
}

.social-icon svg {
  transition: 0.3s ease-in-out;
}

.instagram:hover svg {
  scale: 1.4;
}

.x:hover svg,
.discord:hover svg {
  scale: 1.25;
}

.instagram:hover:after,
.x:hover:after,
.discord:hover:after {
  scale: 4;
  transform: translateX(0.09em) translateY(0.09em);
}

/* 流星效果 */
.instagram::before {
  content: '';
  position: absolute;
  top: -700%;
  left: 1050%;
  rotate: -45deg;
  width: 5em;
  height: 1px;
  background: linear-gradient(90deg, #ffffff, transparent);
  animation: 4s shootingStar ease-in-out infinite;
  transition: 1s ease;
  animation-delay: 1s;
}

.x::before {
  content: '';
  position: absolute;
  top: -1300%;
  left: 850%;
  rotate: -45deg;
  width: 5em;
  height: 1px;
  background: linear-gradient(90deg, #ffffff, transparent);
  animation: 4s shootingStar ease-in-out infinite;
  animation-delay: 3s;
}

.discord::before {
  content: '';
  position: absolute;
  top: -2100%;
  left: 850%;
  rotate: -45deg;
  width: 5em;
  height: 1px;
  background: linear-gradient(90deg, #ffffff, transparent);
  animation: 4s shootingStar ease-in-out infinite;
  animation-delay: 5s;
}

.team-member-card:hover .instagram::before,
.team-member-card:hover .x::before,
.team-member-card:hover .discord::before {
  filter: blur(3px);
}

/* 动画关键帧 */
@keyframes shootingStar {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-55em) translateY(0);
    opacity: 1;
  }
  70% {
    transform: translateX(-70em) translateY(0);
    opacity: 0;
  }
  100% {
    transform: translateX(0) translateY(0);
    opacity: 0;
  }
}

@keyframes move {
  0% {
    transform: translateX(0em) translateY(0em);
  }
  25% {
    transform: translateY(-1em) translateX(-1em);
    rotate: -10deg;
  }
  50% {
    transform: translateY(1em) translateX(-1em);
  }
  75% {
    transform: translateY(-1.25em) translateX(1em);
    rotate: 10deg;
  }
  100% {
    transform: translateX(0em) translateY(0em);
  }
}

@keyframes glowing-stars {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.honors-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.honors-section .container {
  max-width: 1200px !important; /* 限制容器最大宽度 */
  margin: 0 auto !important; /* 居中显示 */
  padding-left: 15px !important; /* 重置左内边距 */
  padding-right: 15px !important; /* 重置右内边距 */
}

/* 荣誉资质全屏样式 */
.honors-section {
  min-height: 100vh;
  padding: 80px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
}

.honors-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
}

.honors-section .section-title {
  font-size: 2.5vw;
  color: #2c5aa0;
  margin-bottom: 10px;
  text-align: center;
  font-weight: bold;
}

.honors-section .section-subtitle {
  font-size: 4.2vw;
  color: #666;
  text-align: center;
  margin-bottom: 10px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.honors-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 1500px;
  margin: 0 auto;
}

.honor-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: auto;
  display: flex;
  flex-direction: column;
  min-width: 320px;
}

.honor-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.honor-image {
  width: 100%;
  height: 400px;
  overflow: hidden;
  position: relative;
}

.honor-certificate {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.honor-card:hover .honor-certificate {
  transform: scale(1.05);
}

.honor-info {
  padding: 25px;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.honor-info h4 {
  color: #2c5aa0;
  margin-bottom: 15px;
  font-size: 1.4rem;
  font-weight: bold;
}

.honor-info p {
  color: #666;
  line-height: 1.6;
  font-size: 1rem;
}

/* 装饰分割线样式 */
.honors-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60px;
  width: 100%;
}

.divider-line {
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #2c5aa0 50%,
    transparent 100%
  );
  flex: 1;
  max-width: 200px;
}

.divider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #2c5aa0;
  margin: 0 20px;
  position: relative;
}

.divider-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
}

/* 新的发展历程样式 */
.development-section {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  padding: 0;
  box-sizing: border-box;
}

.development-background {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  transition: all 0.8s ease-in-out;
  filter: blur(0px); /* 默认清晰 */
}

.development-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1); /* 默认较少遮罩 */
  transition: all 0.6s ease-in-out;
  z-index: 1;
}

.development-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 60px;
  box-sizing: border-box;
  z-index: 2;
  transition: background 0.6s ease-in-out;
}

/* 悬停时的朦胧到清晰过渡效果 */
.development-section.has-hover .development-background {
  animation: blurToClear 1.2s ease-out forwards;
  transform: scale(1.01);
}

.development-section.has-hover .development-background::before {
  animation: fadeOverlay 1.2s ease-out forwards;
}

/* 动画关键帧 - 朦胧到清晰 */
@keyframes blurToClear {
  0% {
    filter: blur(4px) brightness(0.8);
    transform: scale(1);
  }
  30% {
    filter: blur(2px) brightness(0.9);
    transform: scale(1.005);
  }
  70% {
    filter: blur(0.5px) brightness(1);
    transform: scale(1.01);
  }
  100% {
    filter: blur(0px) brightness(1.1);
    transform: scale(1.01);
  }
}

/* 动画关键帧 - 遮罩淡化 */
@keyframes fadeOverlay {
  0% {
    background: rgba(0, 0, 0, 0.5);
  }
  30% {
    background: rgba(0, 0, 0, 0.35);
  }
  70% {
    background: rgba(0, 0, 0, 0.2);
  }
  100% {
    background: rgba(0, 0, 0, 0.05);
  }
}

.development-header {
  color: white;
  text-align: left;
}

.development-label {
  font-size: 1rem;
  letter-spacing: 2px;
  margin-bottom: 10px;
  opacity: 0.9;
  text-transform: uppercase;
}

.development-title-cn {
  font-size: 2.8rem;
  font-weight: 400;
  margin-bottom: 8px;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.development-title-en {
  font-size: 4.5rem;
  font-weight: 800;
  letter-spacing: 4px;
  margin-bottom: 0;
  text-transform: uppercase;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  line-height: 0.9;
}

.development-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin: 40px 0;
  max-width: 1400px;
  width: 100%;
  min-height: 300px;
}

.development-info {
  flex: 0 0 50%;
  color: white;
  padding-right: 60px;
  padding-left: 100px; /* 向右移动文字 */
  animation: fadeInLeft 0.8s ease-out;
  transition: all 0.3s ease;
  order: 1; /* 文字在左边 */
}

.development-section.has-hover .development-info {
  animation: contentSlideIn 0.8s ease-out;
}

.development-date {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 25px;
  color: #64b5f6;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.development-details h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 18px;
  line-height: 1.3;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.development-details p {
  font-size: 1.2rem;
  line-height: 1.7;
  margin-bottom: 12px;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.development-extra {
  font-size: 1.1rem;
  color: #64b5f6;
  font-weight: 600;
  margin-top: 20px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.development-image {
  flex: 0 0 45%;
  text-align: right;
  animation: fadeInRight 0.8s ease-out;
  order: 2; /* 图片在右边 */
}

.development-image img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.development-image img:hover {
  transform: scale(1.05);
}

/* 时间轴导航样式 */
.timeline-navigation {
  width: 100%;
  padding: 0 120px; /* 增加左右内边距，让线条更长 */
  margin-bottom: 20px;
}

.timeline-track {
  position: relative;
  height: 10px; /* 增加线条高度 */
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px; /* 相应调整圆角 */
  margin-bottom: 40px; /* 增加底部间距 */
  backdrop-filter: blur(10px);
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #64b5f6 0%, #42a5f5 100%);
  border-radius: 5px; /* 相应调整圆角 */
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 15px rgba(100, 181, 246, 0.5); /* 增强阴影效果 */
}

.timeline-point {
  position: absolute;
  top: 100%;
  transform: translate(-50%, 10px); /* 调整位置适应更粗的线条 */
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-point-inner {
  width: 16px; /* 增大点的尺寸 */
  height: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3); /* 增强阴影 */
}

.timeline-point.active .timeline-point-inner {
  background: #ffffff;
  transform: scale(1.4);
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.4);
}

.timeline-point:hover .timeline-point-inner,
.timeline-point.hovered .timeline-point-inner {
  background: #ffffff;
  transform: scale(1.2);
  box-shadow: 0 3px 12px rgba(255, 255, 255, 0.3);
}

.timeline-date-label {
  position: absolute;
  top: 30px; /* 调整位置适应更大的点 */
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem; /* 增大文字尺寸 */
  font-weight: 500; /* 增加字重 */
  white-space: nowrap;
  transition: all 0.3s ease;
  letter-spacing: 0.8px; /* 增加字间距 */
}

.timeline-point.active .timeline-date-label {
  color: #ffffff;
  font-weight: 600; /* 增加激活状态字重 */
  transform: translateX(-50%) scale(1.1); /* 增大缩放比例 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* 添加文字阴影 */
}

.timeline-point:hover .timeline-date-label,
.timeline-point.hovered .timeline-date-label {
  color: #ffffff;
  font-weight: 600; /* 增加悬停状态字重 */
  transform: translateX(-50%) scale(1.05); /* 增大缩放比例 */
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
}

/* 动画效果 */
@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes contentSlideIn {
  0% {
    opacity: 0.7;
    transform: translateX(-20px);
  }
  50% {
    opacity: 0.9;
    transform: translateX(5px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .mission-vision-section .container {
    max-width: 100%;
    padding: 0 20px;
  }

  .honors-container {
    padding: 0 15px;
    max-width: 1400px;
  }

  .honors-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    max-width: 1200px;
  }

  .honor-card {
    min-width: 280px;
  }

  .mission-card {
    padding: 30px 15px; /* 减少卡片内边距 */
  }

  .honor-image {
    height: 350px;
  }

  .honors-divider {
    margin-top: 50px;
  }

  .divider-line {
    max-width: 150px;
  }

  /* 公司简介中等屏幕调整 */
  .profile-content {
    gap: 40px;
    padding: 0 15px;
  }

  .profile-text {
    flex: 0 0 45%;
    max-width: 450px;
    padding-right: 10px;
  }

  .profile-title-cn {
    font-size: 2rem;
  }

  .profile-title-en {
    font-size: 3.2rem;
    margin-bottom: 30px;
  }

  .profile-description p {
    font-size: 1.3rem;
  }

  /* 中等屏幕上的团队卡片调整 */
  .team-member-card {
    width: 24em; /* 保持较大尺寸 */
    height: 30em;
    padding: 1.3em 2em 1.3em 1.3em;
  }

  .team-member-card .member-image {
    width: 9.5em; /* 保持较大头像 */
    height: 9.5em;
  }

  .member-heading {
    font-size: 2rem; /* 保持较大文字 */
  }

  .member-position-badge {
    font-size: 1.1rem; /* 调整职位标签 */
    padding: 9px 20px;
  }

  .member-description {
    font-size: 1rem; /* 保持较大描述文字 */
    padding: 0 15px;
  }

  /* 发展历程中等屏幕响应式 */
  .development-overlay {
    padding: 40px 30px;
  }

  .development-title-cn {
    font-size: 2.2rem;
  }

  .development-title-en {
    font-size: 3.5rem;
  }

  .development-info {
    padding-right: 140px;
    padding-left: 180px; /* 中等屏幕上也向右移动文字 */
  }

  .development-date {
    font-size: 2.8rem;
  }

  .development-details h3 {
    font-size: 1.6rem;
  }

  /* 时间轴中等屏幕响应式 */
  .timeline-navigation {
    padding: 0 80px; /* 保持较长的线条 */
  }

  .timeline-track {
    height: 8px; /* 中等屏幕稍微减小线条高度 */
  }

  .timeline-point-inner {
    width: 14px; /* 保持较大的点 */
    height: 14px;
  }

  .timeline-date-label {
    font-size: 1rem; /* 保持较大的文字 */
    top: 28px;
  }
}

@media (max-width: 900px) {
  /* 在900px以下才让团队卡片换行 */
  .team-grid {
    flex-wrap: wrap;
    justify-content: center;
  }

  .team-member-card {
    width: 22em; /* 保持合适尺寸 */
    height: 28em;
    margin-bottom: 25px;
  }

  .team-member-card .member-image {
    width: 8.5em; /* 保持较大头像 */
    height: 8.5em;
  }

  .member-heading {
    font-size: 1.8rem; /* 保持较大文字 */
  }

  .member-position-badge {
    font-size: 1rem;
    padding: 8px 18px;
  }

  .member-description {
    font-size: 0.95rem; /* 保持较大描述文字 */
    padding: 0 12px;
  }
}

@media (max-width: 768px) {
  .banner {
    font-size: 2rem;
    padding: 50px 0;
  }

  /* 公司简介响应式 */
  .profile-content {
    padding: 0 15px;
  }

  .profile-text {
    padding-right: 0;
    margin-bottom: 30px;
    text-align: center;
  }

  .profile-label {
    font-size: 1rem;
  }

  .profile-title-cn {
    font-size: 1.8rem;
  }

  .profile-title-en {
    font-size: 2.8rem;
    margin-bottom: 25px;
    letter-spacing: -1px;
    word-spacing: 4px;
  }

  .profile-description p {
    font-size: 1.1rem;
    text-align: left;
    margin-bottom: 20px;
  }

  .profile-image {
    flex: none;
    margin-top: 0;
  }

  .image-decoration {
    width: 80px;
    height: 20px;
    bottom: -8px;
    right: -8px;
  }

  .mission-vision-section {
    padding: 50px 0; /* 减少上下边距 */
  }

  .mission-card {
    padding: 25px 15px; /* 进一步减少卡片内边距 */
    margin-bottom: 20px;
  }

  .honors-section {
    padding: 60px 0;
    min-height: auto;
  }

  .honors-section .section-title {
    font-size: 2.5rem;
  }

  /* 团队标题响应式 */
  .team-section .section-title {
    font-size: 2.5rem;
  }

  .team-section .section-subtitle {
    font-size: 3.5rem;
  }

  .honors-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .honor-image {
    height: 300px;
  }

  .honor-info {
    padding: 20px;
  }

  .mission-card h4 {
    font-size: 1.3rem; /* 调整标题大小 */
  }

  .mission-card p {
    font-size: 0.95rem; /* 调整文字大小 */
  }

  .honor-info h4 {
    font-size: 1.2rem;
  }

  .honor-info p {
    font-size: 0.9rem;
  }

  .honors-divider {
    margin-top: 40px;
  }

  .divider-line {
    max-width: 100px;
  }

  .divider-dot {
    width: 10px;
    height: 10px;
    margin: 0 15px;
  }

  .divider-dot::before {
    width: 4px;
    height: 4px;
  }

  /* 团队成员卡片响应式 */
  .team-grid {
    flex-direction: column;
    align-items: center;
    gap: 25px;
  }

  .team-member-card {
    width: 90%;
    max-width: 350px; /* 增加最大宽度 */
    height: auto;
    min-height: 450px; /* 增加最小高度 */
    padding: 1.8em; /* 增加内边距 */
  }

  .team-member-card .member-image {
    width: 7.5em; /* 增大头像 */
    height: 7.5em;
  }

  .member-heading {
    font-size: 1.5rem; /* 增大姓名文字 */
  }

  .member-position-badge {
    font-size: 1rem; /* 增大职位文字 */
    padding: 6px 15px;
  }

  .member-description {
    font-size: 0.9rem; /* 增大描述文字 */
    padding: 0 10px;
  }

  /* 调整星空效果在小屏幕上的显示 */
  .member-heading::before,
  .member-icons::before,
  .member-icons::after {
    display: none;
  }

  /* 调整流星效果 */
  .instagram::before,
  .x::before,
  .discord::before {
    display: none;
  }

  /* 发展历程响应式 */
  .development-overlay {
    padding: 30px 20px;
  }

  .development-title-cn {
    font-size: 1.8rem;
  }

  .development-title-en {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }

  .development-content {
    flex-direction: column;
    text-align: center;
  }

  .development-info {
    flex: none;
    padding-right: 0;
    padding-left: 0; /* 小屏幕上重置左边距 */
    margin-bottom: 30px;
  }

  .development-date {
    font-size: 2.5rem;
  }

  .development-details h3 {
    font-size: 1.5rem;
  }

  .development-details p {
    font-size: 1rem;
  }

  .development-image {
    flex: none;
    text-align: center;
  }

  .timeline-navigation {
    padding: 0 30px; /* 小屏幕也保持一定的线条长度 */
  }

  .timeline-track {
    height: 6px; /* 小屏幕适当减小线条高度 */
  }

  .timeline-point-inner {
    width: 12px; /* 小屏幕保持合适的点大小 */
    height: 12px;
  }

  .timeline-date-label {
    font-size: 0.9rem; /* 增大小屏幕文字尺寸 */
    top: 25px;
    font-weight: 500; /* 增加字重 */
  }
}
</style>
