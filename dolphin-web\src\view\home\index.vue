<template>
  <div id="HomePage">
    <!-- 轮播图 -->
    <swiper
      id="swiper"
      :modules="modules"
      :slides-per-view="1"
      :space-between="0"
      lazy
      loop
      :autoplay="{
        delay: 4000,
        disableOnInteraction: false,
        pauseOnMouseEnter: true
      }"
      :pagination="{
        clickable: true
      }"
    >
      <swiper-slide
        class="banner-swiper"
        v-for="(item, index) in swiperList"
        :key="index"
      >
        <img class="swiper-lazy" :data-src="item.img" alt="轮播图" />
        <div class="swiper-lazy-preloader"></div>
        <div class="swiper-slide-title">
          <h1>{{ item.title }}</h1>
          <p>{{ item.content }}</p>
        </div>
      </swiper-slide>
    </swiper>

    <!-- 医疗超声大模型 -->
    <div id="medicalAI" class="container-fuild">
      <div class="row medicalAI-container">
        <div class="col-xs-12 col-sm-12 col-md-6 wow zoomIn">
          <img
            class="img-responsive"
            src="@/assets/img/banner4.jpg"
            alt="医疗超声大模型"
          />
        </div>
        <div class="col-xs-12 col-sm-12 col-md-6">
          <h2 class="medicalAI-title">
            医疗超声大模型
            <small>/ Medical Ultrasound AI Model</small>
          </h2>
          <p>
            基于深度学习技术的医疗超声影像智能分析系统，能够自动识别和分析超声影像中的病灶信息，为医生提供精准的诊断辅助。我们的大模型经过数百万张医学影像的训练，在多种疾病的检测上达到了95%以上的准确率。系统采用先进的卷积神经网络架构，结合多尺度特征提取和注意力机制，能够精确定位病变区域并进行量化分析。
          </p>
          
          <p>
            我们的AI模型涵盖心脏超声、腹部超声、妇产科超声、甲状腺超声等多个专科领域，能够识别包括心脏瓣膜病变、肝脏肿瘤、胎儿畸形、甲状腺结节等在内的200多种病变类型。系统还具备智能测量功能，可自动计算心脏射血分数、胎儿双顶径、肿瘤体积等关键指标，确保测量结果的一致性和准确性。
          </p>
          <p>
            平台采用云端部署架构，支持远程诊断和多院区协同，配备完善的数据安全保护机制，符合医疗数据隐私保护标准。同时提供7×24小时技术支持服务，确保系统稳定运行。我们已为全国超过500家医疗机构提供服务，累计处理超声影像超过1000万例，获得了医生和患者的广泛认可。
          </p>
          <h2 class="medicalAI-device">医院/诊所/体检中心 &nbsp; 全场景应用</h2>
          <a href="javascript:;" class="btn btn-lg btn-block btn-primary"
            >了解更多</a
          >
        </div>
      </div>
    </div>



    <!-- 为什么选择我们 -->
    <div id="whyChooseUs" class="container-fluid">
      <div class="container">
        <div class="whyChooseUs-title text-center">
          <p class="main-title">为什么选择我们的服务</p>
          <p class="sub-title">THE REASON TO CHOOSING US</p>
          <div class="title-divider"></div>
        </div>
        <div class="row services-row">
          <div
            class="col-xs-12 col-sm-6 col-md-3 server-wrapper"
            v-for="(item, index) in serverList"
            :key="index"
          >
            <div
              class="server-block wow slideInUp"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
            >
              <div class="icon-wrapper">
                <img class="service-icon" :src="item.logo" alt="logo" />
              </div>
              <h4 class="service-title">{{ item.title }}</h4>
              <div
                class="service-content"
                v-html="item.content"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="HomePage">
import WOW from 'wow.js'
import { onMounted } from 'vue'
// import Swiper from 'swiper'
import { Navigation, Pagination, Scrollbar, A11y, Lazy, Autoplay } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/scrollbar'
import 'swiper/css/lazy'
import 'swiper/css/autoplay'

import banner1 from '@/assets/img/banner3.png'
import banner2 from '@/assets/img/banner2.jpg'
import banner4 from '@/assets/img/banner4.jpg'
import health from '@/assets/img/health.jpg'



import img_tel from '@/assets/img/tel.png'
import img_computer from '@/assets/img/computer.png'
import img_qq from '@/assets/img/qq.png'
import img_skill from '@/assets/img/skill.png'

const swiperList = [
  {
    img: banner1,
    title: '医疗超声大模型',
    content: '基于深度学习的医疗超声影像智能分析，让诊断更精准'
  },
  {
    img: banner2,
    title: '',
    content: ''
  },
  {
    img: banner4,
    title: '智能影像分析',
    content: '自动识别病灶，精准测量分析，提升医生工作效率'
  },
  {
    img: health,
    title: '多模态医学支持',
    content: '融合图像与文本信息，为临床决策提供多维数据参考'
  }
]

const modules = [Navigation, Pagination, Scrollbar, A11y, Lazy, Autoplay]



const serverList = [
  {
    logo: img_tel,
    title: '专业技术支持',
    content: '<p>7x24小时技术支持服务</p>专业工程师快速响应，解决技术问题'
  },
  {
    logo: img_computer,
    title: '远程部署服务',
    content: '<p>提供远程部署和调试服务</p>确保系统快速上线，稳定运行'
  },
  {
    logo: img_qq,
    title: '在线培训指导',
    content: '<p>提供专业的在线培训服务</p>帮助医护人员快速掌握系统操作'
  },
  {
    logo: img_skill,
    title: '定制化解决方案',
    content: '<p>根据医院需求定制AI模型</p>提供个性化的医疗AI解决方案'
  }
]

// 鼠标事件处理函数
const handleMouseEnter = (event) => {
  const block = event.currentTarget
  block.style.transform = 'translateY(-10px)'
  block.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.1)'
}

const handleMouseLeave = (event) => {
  const block = event.currentTarget
  block.style.transform = 'translateY(0)'
  block.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.08)'
}

// 联系我们按钮事件处理
const handleConsultation = () => {
  // 可以跳转到联系页面或显示联系信息
  window.location.href = '/contact'
}

const handleEmailContact = () => {
  // 打开邮件客户端
  window.location.href = 'mailto:<EMAIL>?subject=医疗AI咨询&body=您好，我想了解更多关于医疗AI解决方案的信息。'
}

onMounted(() => {
  /* wowjs动画 */
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
/* 整体盒子 */
#HomePage {
  width: 100%;
}

/* 顶部轮播图 */
#swiper {
  width: 100%;
  height: 100vh;
  margin-top: 0;
  padding-top: 0;
  position: relative;
}

#swiper .banner-swiper {
  width: 100%;
  height: 100%;
  position: relative;
}

#swiper .banner-swiper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#swiper .banner-swiper .swiper-slide-title {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999999999;
  width: 100%;
  height: 100%;
  color: #fff;
  background: rgba(0, 0, 0, 0.2);
  text-align: center;
  line-height: 80px;
}

#swiper .banner-swiper .swiper-slide-title > h1 {
  font-size: 50px;
  margin-top: 20%;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.95);
}

#swiper .banner-swiper .swiper-slide-title > p {
  font-size: 20px;
  margin-top: 1%;
  font-weight: 500;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.9);
}

/* 医疗超声大模型 */
#medicalAI {
  padding: 100px;
  transition: all ease 0.6s;
  box-sizing: border-box;
}

#medicalAI .medicalAI-title {
  padding-bottom: 10px;
  border-bottom: 1px solid #2c5aa0;
  color: #2c5aa0;
}

#medicalAI p {
  font-size: 14px;
  color: #333;
  line-height: 2rem;
}

#medicalAI .medicalAI-device {
  margin: 50px 0 20px;
  color: #2c5aa0;
}

/* 医疗AI专家 */
.contact-expert-section {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}

.contact-expert-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.expert-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.expert-text {
  margin-bottom: 40px;
}

.expert-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #ffffff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.expert-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  font-weight: 400;
  line-height: 1.6;
}

.expert-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.expert-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 160px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.expert-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.expert-btn:active::before {
  width: 300px;
  height: 300px;
}

.primary-btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.primary-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.secondary-btn {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 1.1rem;
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

.expert-btn:hover .btn-icon {
  transform: scale(1.1);
}



/* 为什么选择我们 */
#whyChooseUs {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

#whyChooseUs .whyChooseUs-title {
  margin-bottom: 60px;
}

#whyChooseUs .whyChooseUs-title .main-title {
  font-size: 32px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
}

#whyChooseUs .whyChooseUs-title .sub-title {
  font-size: 16px;
  color: #7f8c8d;
  letter-spacing: 2px;
  margin-bottom: 20px;
}

#whyChooseUs .whyChooseUs-title .title-divider {
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2980b9);
  margin: 0 auto;
  border-radius: 2px;
}

#whyChooseUs .services-row {
  display: flex;
  align-items: stretch;
  margin: 0 -15px;
}

#whyChooseUs .server-wrapper {
  padding: 0 15px;
  margin-bottom: 30px;
  display: flex;
}

#whyChooseUs .server-block {
  background: #ffffff;
  padding: 40px 25px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  width: 100%;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
  position: relative;
  overflow: hidden;
}

#whyChooseUs .server-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

#whyChooseUs .server-block:hover::before {
  transform: scaleX(1);
}



#whyChooseUs .icon-wrapper {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

#whyChooseUs .server-block:hover .icon-wrapper {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

#whyChooseUs .service-icon {
  width: 40px;
  height: 40px;
  filter: brightness(0) invert(1);
}

#whyChooseUs .service-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.3;
}

#whyChooseUs .service-content {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  text-align: center;
  min-height: 60px;
}

#whyChooseUs .service-content p {
  color: #3498db;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 13px;
}

/* 媒体查询（手机） */
@media screen and (max-width: 768px) {
  #swiper {
    height: 200px;
  }

  #bigData {
    padding: 30px;
  }

  #bigData .bigData-title {
    font-size: 20px;
  }

  #bigData .bigData-device {
    font-size: 20px;
    margin: 10px 0 10px;
  }

  .contact-expert-section {
    padding: 40px 0;
  }

  .expert-title {
    font-size: 1.8rem;
    margin-bottom: 10px;
  }

  .expert-subtitle {
    font-size: 1rem;
    margin-bottom: 25px;
  }

  .expert-actions {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .expert-btn {
    width: 200px;
    padding: 12px 20px;
    font-size: 0.9rem;
  }



  #whyChooseUs {
    padding: 40px 0;
  }

  #whyChooseUs .whyChooseUs-title {
    margin-bottom: 40px;
  }

  #whyChooseUs .whyChooseUs-title .main-title {
    font-size: 24px;
    font-weight: 600;
  }

  #whyChooseUs .whyChooseUs-title .sub-title {
    font-size: 14px;
  }

  #whyChooseUs .services-row {
    margin: 0 -10px;
  }

  #whyChooseUs .server-wrapper {
    padding: 0 10px;
    margin-bottom: 20px;
  }

  #whyChooseUs .server-block {
    padding: 30px 20px;
    margin-bottom: 20px;
    min-height: 240px;
  }

  #whyChooseUs .icon-wrapper {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
  }

  #whyChooseUs .service-icon {
    width: 30px;
    height: 30px;
  }

  #whyChooseUs .service-title {
    font-size: 18px;
    margin-bottom: 15px;
  }

  #whyChooseUs .service-content {
    font-size: 13px;
  }
}

/* 媒体查询（平板） */
@media screen and (min-width: 768px) and (max-width: 996px) {
  #swiper {
    height: 400px;
  }

  #bigData {
    padding: 60px;
  }

  #bigData .bigData-title {
    font-size: 30px;
  }

  #bigData .bigData-device {
    font-size: 30px;
    margin: 30px 0 15px;
  }

  .contact-expert-section {
    padding: 50px 0;
  }

  .expert-title {
    font-size: 2.2rem;
    margin-bottom: 12px;
  }

  .expert-subtitle {
    font-size: 1.1rem;
    margin-bottom: 30px;
  }

  .expert-actions {
    gap: 18px;
  }

  .expert-btn {
    min-width: 180px;
    padding: 14px 25px;
  }



  #whyChooseUs {
    padding: 60px 0;
  }

  #whyChooseUs .whyChooseUs-title .main-title {
    font-size: 28px;
  }

  #whyChooseUs .whyChooseUs-title .sub-title {
    font-size: 15px;
  }

  #whyChooseUs .services-row {
    margin: 0 -12px;
  }

  #whyChooseUs .server-wrapper {
    padding: 0 12px;
    margin-bottom: 25px;
  }

  #whyChooseUs .server-block {
    padding: 35px 22px;
    min-height: 260px;
  }

  #whyChooseUs .icon-wrapper {
    width: 70px;
    height: 70px;
    margin-bottom: 22px;
  }

  #whyChooseUs .service-icon {
    width: 35px;
    height: 35px;
  }

  #whyChooseUs .service-title {
    font-size: 19px;
    margin-bottom: 18px;
  }
}
</style>

