<template>
  <div id="Cases">
    <!-- <div class="cases-header">
      <div class="container">
        <h1 class="wow fadeInUp">成功案例</h1>
        <p class="wow fadeInUp" data-wow-delay="0.2s">
          我们的医疗AI解决方案已在全国多家三甲医院成功落地应用
        </p>
      </div>
    </div> -->

    <!-- 统计数据Banner -->
    <div class="stats-banner">
      <div class="stats-overlay">
        <div class="stats-content">
          <div class="container">
            <div class="row">
              <div class="col-md-3 col-sm-6">
                <div class="outer">
                  <div class="dot"></div>
                  <div class="card">
                    <div class="ray"></div>
                    <div class="text">50+</div>
                    <div>合作医院</div>
                    <div class="line topl"></div>
                    <div class="line leftl"></div>
                    <div class="line bottoml"></div>
                    <div class="line rightl"></div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6">
                <div class="outer">
                  <div class="dot"></div>
                  <div class="card">
                    <div class="ray"></div>
                    <div class="text">100万+</div>
                    <div>处理影像数量</div>
                    <div class="line topl"></div>
                    <div class="line leftl"></div>
                    <div class="line bottoml"></div>
                    <div class="line rightl"></div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6">
                <div class="outer">
                  <div class="dot"></div>
                  <div class="card">
                    <div class="ray"></div>
                    <div class="text">95%</div>
                    <div>诊断准确率</div>
                    <div class="line topl"></div>
                    <div class="line leftl"></div>
                    <div class="line bottoml"></div>
                    <div class="line rightl"></div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6">
                <div class="outer">
                  <div class="dot"></div>
                  <div class="card">
                    <div class="ray"></div>
                    <div class="text">60%</div>
                    <div>效率提升</div>
                    <div class="line topl"></div>
                    <div class="line leftl"></div>
                    <div class="line bottoml"></div>
                    <div class="line rightl"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例展示 - 全宽背景 -->
    <div class="cases-showcase">
      <div class="container">
        <h2 class="section-title wow fadeInUp">典型案例</h2>
        <div class="cases-grid">
          <div
            class="modern-card wow slideInUp"
            v-for="(caseItem, index) in casesList"
            :key="index"
            :data-wow-delay="`${index * 0.1}s`"
          >
            <!-- 背景渐变效果 -->
            <div class="card-gradient-bg"></div>
            <div class="card-inner-bg"></div>

            <div class="card-content">
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="header-left">
                  <div class="icon-container">
                    <svg
                      class="icon"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      ></path>
                    </svg>
                  </div>
                  <h3 class="card-title">{{ caseItem.hospital }}</h3>
                </div>
                <span class="status-badge">
                  <span class="status-dot"></span>
                  已部署
                </span>
              </div>

              <!-- 案例类型 -->
              <div class="case-type-section">
                <p class="case-type-text">{{ caseItem.type }}</p>
                <p class="case-description">{{ caseItem.description }}</p>
              </div>

              <!-- 数据展示区域 -->
              <div class="metrics-grid">
                <div
                  class="metric-item"
                  v-for="(result, idx) in caseItem.results.slice(0, 2)"
                  :key="idx"
                >
                  <p class="metric-label">{{ result.label }}</p>
                  <p class="metric-value">{{ result.value }}</p>
                  <span class="metric-trend"
                    >+{{ Math.floor(Math.random() * 20 + 5) }}%</span
                  >
                </div>
              </div>

              <!-- 图表区域 -->
              <div class="chart-container">
                <div class="chart-bars">
                  <div
                    class="chart-bar"
                    v-for="i in 7"
                    :key="i"
                    :style="{
                      height: Math.floor(Math.random() * 80 + 20) + '%'
                    }"
                  >
                    <div
                      class="bar-fill"
                      :style="{
                        height: Math.floor(Math.random() * 60 + 40) + '%'
                      }"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 底部操作区域 -->
              <div class="card-footer">
                <div class="footer-left">
                  <span class="time-text">最近7天</span>
                  <svg
                    class="dropdown-icon"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </div>
                <button class="view-details-btn">
                  查看详情
                  <svg
                    class="arrow-icon"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- CTA区域 -->
      <div class="cta-section wow fadeInUp">
        <div class="cta-content">
          <h2>成为我们的下一个成功案例</h2>
          <p>联系我们，了解如何将AI技术应用到您的医疗机构</p>

          <!-- 新的交互式按钮 -->
          <div class="transaction-container">
            <div class="left-side">
              <div class="card">
                <div class="card-line"></div>
                <div class="buttons"></div>
              </div>
              <div class="post">
                <div class="post-line"></div>
                <div class="screen">
                  <div class="dollar">$</div>
                </div>
                <div class="numbers"></div>
                <div class="numbers-line2"></div>
              </div>
            </div>
            <div class="right-side">
              <div class="new">New Transaction</div>
              <svg
                viewBox="0 0 451.846 451.847"
                height="512"
                width="512"
                xmlns="http://www.w3.org/2000/svg"
                class="arrow"
              >
                <path
                  fill="#cfcfcf"
                  data-old_color="#000000"
                  class="active-path"
                  data-original="#000000"
                  d="M345.441 248.292L151.154 442.573c-12.359 12.365-32.397 12.365-44.75 0-12.354-12.354-12.354-32.391 0-44.744L278.318 225.92 106.409 54.017c-12.354-12.359-12.354-32.394 0-44.748 12.354-12.359 32.391-12.359 44.75 0l194.287 194.284c6.177 6.18 9.262 14.271 9.262 22.366 0 8.099-3.091 16.196-9.267 22.373z"
                ></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Cases">
import { onMounted } from 'vue'
import WOW from 'wow.js'
import hospitalImg1 from '@/assets/img/service1.jpg'
import hospitalImg2 from '@/assets/img/service2.jpg'
import hospitalImg3 from '@/assets/img/service3.jpg'

const casesList = [
  {
    hospital: '北京协和医院',
    type: '超声科AI辅助诊断系统',
    description:
      '部署超声影像AI分析系统，实现甲状腺结节自动检测和良恶性判断，显著提升诊断效率和准确性。',
    image: hospitalImg1,
    results: [
      { value: '95%', label: '诊断准确率' },
      { value: '60%', label: '效率提升' },
      { value: '30%', label: '误诊率降低' }
    ]
  },
  {
    hospital: '上海瑞金医院',
    type: '心脏超声智能分析平台',
    description:
      '建设心脏超声AI分析平台，自动测量心脏功能参数，辅助心脏疾病诊断和治疗方案制定。',
    image: hospitalImg2,
    results: [
      { value: '92%', label: '检测准确率' },
      { value: '50%', label: '报告生成速度提升' },
      { value: '40%', label: '医生工作量减少' }
    ]
  },
  {
    hospital: '广州中山大学附属第一医院',
    type: '妇产科超声AI系统',
    description:
      '部署妇产科超声AI辅助系统，实现胎儿发育异常自动筛查，提高产前诊断的准确性和效率。',
    image: hospitalImg3,
    results: [
      { value: '98%', label: '筛查准确率' },
      { value: '70%', label: '检查时间缩短' },
      { value: '25%', label: '漏诊率降低' }
    ]
  }
]

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 0,
    mobile: true,
    live: true
  }).init()
})
</script>

<style scoped>
#Cases {
  padding: 0;
}

.cases-header {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.cases-header h1 {
  font-size: 3rem;
  margin-bottom: 20px;
}

.cases-header p {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
}

/* 统计数据Banner区域样式 */
.stats-banner {
  position: relative;
  height: 100vh;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  background-image: url('@/assets/img/failure/banner.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.stats-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-content {
  width: 100%;
  text-align: center;
  color: white;
}

.stats-content .row {
  gap: 40px;
  justify-content: center;
}

.stats-content .col-md-3 {
  margin-bottom: 40px;
  padding: 0 20px;
}

.outer {
  width: 300px;
  height: 250px;
  border-radius: 10px;
  /* padding: 1px;
  background: radial-gradient(
    circle 230px at 0% 0%,
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.1)
  ); */
  position: relative;
  margin: 0 auto;
}

.dot {
  width: 5px;
  aspect-ratio: 1;
  position: absolute;
  background-color: #fff;
  box-shadow: 0 0 10px #ffffff;
  border-radius: 100px;
  z-index: 2;
  right: 10%;
  top: 10%;
  animation: moveDot 6s linear infinite;
}

@keyframes moveDot {
  0%,
  100% {
    top: 10%;
    right: 10%;
  }
  25% {
    top: 10%;
    right: calc(100% - 35px);
  }
  50% {
    top: calc(100% - 30px);
    right: calc(100% - 35px);
  }
  75% {
    top: calc(100% - 30px);
    right: 10%;
  }
}

.card {
  z-index: 1;
  width: 55%;
  height: 55%;
  border-radius: 9px;
  border: solid 1px rgba(255, 255, 255, 0.2);
  background-size: 20px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  flex-direction: column;
  color: #fff;
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
}

.ray {
  width: 220px;
  height: 45px;
  border-radius: 100px;
  position: absolute;
  background-color: #c7c7c7;
  opacity: 0.4;
  box-shadow: 0 0 50px #fff;
  filter: blur(10px);
  transform-origin: 10%;
  top: 0%;
  left: 0;
  transform: rotate(40deg);
}

.card .text {
  font-weight: bolder;
  font-size: 4rem;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.line {
  width: 100%;
  height: 1px;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.3);
}

.topl {
  top: 10%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.5) 30%,
    rgba(255, 255, 255, 0.2) 70%
  );
}

.bottoml {
  bottom: 10%;
}

.leftl {
  left: 10%;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.5) 30%,
    rgba(255, 255, 255, 0.2) 70%
  );
}

.rightl {
  right: 10%;
  width: 1px;
  height: 100%;
}

.cases-showcase {
  padding: 80px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  position: relative;
  overflow: hidden;
}

.cases-showcase::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(99, 102, 241, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(236, 72, 153, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.section-title {
  text-align: center;
  color: white;
  font-size: 2.5rem;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-bottom: 50px;
  justify-items: center;
  position: relative;
  z-index: 1;
}

/* 现代化卡片样式 */
.modern-card {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 380px;
  min-height: 480px;
  background: #0f172a;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  overflow: hidden;
}

.modern-card:hover {
  transform: scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(99, 102, 241, 0.2);
}

.card-gradient-bg {
  position: absolute;
  inset: 0;
  border-radius: 12px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%);
  opacity: 0.2;
  filter: blur(4px);
  transition: opacity 0.3s ease;
}

.modern-card:hover .card-gradient-bg {
  opacity: 0.3;
}

.card-inner-bg {
  position: absolute;
  inset: 1px;
  border-radius: 11px;
  background: #0f172a;
}

.card-content {
  position: relative;
  z-index: 1;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
}

.icon {
  width: 16px;
  height: 16px;
  color: white;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 14px;
  font-weight: 500;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b981;
}

.case-type-section {
  margin-bottom: 16px;
}

.case-type-text {
  font-size: 14px;
  font-weight: 500;
  color: #94a3b8;
  margin: 0 0 8px 0;
}

.case-description {
  font-size: 15px;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.metric-item {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 8px;
  padding: 12px;
}

.metric-label {
  font-size: 14px;
  font-weight: 500;
  color: #94a3b8;
  margin: 0 0 4px 0;
}

.metric-value {
  font-size: 22px;
  font-weight: 600;
  color: white;
  margin: 0 0 4px 0;
}

.metric-trend {
  font-size: 14px;
  font-weight: 500;
  color: #10b981;
}

.chart-container {
  width: 100%;
  height: 96px;
  overflow: hidden;
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.5);
  padding: 12px;
  margin-bottom: 16px;
}

.chart-bars {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: flex-end;
  justify-content: space-between;
  gap: 4px;
}

.chart-bar {
  width: 12px;
  border-radius: 2px;
  background: rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.bar-fill {
  width: 100%;
  border-radius: 2px;
  background: #6366f1;
  transition: all 0.3s ease;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-text {
  font-size: 14px;
  font-weight: 500;
  color: #94a3b8;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  color: #94a3b8;
}

.view-details-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 6px 14px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-details-btn:hover {
  background: linear-gradient(135deg, #5b5bd6 0%, #9333ea 100%);
}

.arrow-icon {
  width: 12px;
  height: 12px;
}

.cta-section {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  color: white;
  padding: 80px 40px;
  text-align: center;
  border-radius: 15px;
  margin: 50px 0;
}

.cta-content h2 {
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .stats-banner {
    height: 100vh;
    min-height: 100vh;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    background-attachment: scroll;
    padding: 60px 20px 40px;
  }

  .stats-content .row {
    gap: 20px;
  }

  .stats-content .col-md-3 {
    margin-bottom: 30px;
    padding: 0 15px;
  }

  .outer {
    width: 250px;
    height: 200px;
    margin: 20px auto;
  }

  .card .text {
    font-size: 3rem;
  }

  .ray {
    width: 180px;
    height: 35px;
  }

  .cases-header h1 {
    font-size: 2rem;
  }

  .cases-grid {
    grid-template-columns: 1fr;
    justify-items: center;
  }

  .modern-card {
    width: 100%;
    max-width: 380px;
  }

  .cta-content .btn {
    display: block;
    margin: 10px auto;
    width: 200px;
  }
}

/* 平板设备响应式设计 */
@media (max-width: 1024px) and (min-width: 769px) {
  .cases-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    justify-items: center;
  }

  .modern-card {
    width: 100%;
    max-width: 350px;
  }
}

/* 交互式按钮组件样式 */
.transaction-container {
  background-color: #ffffff;
  display: flex;
  width: 460px;
  height: 120px;
  position: relative;
  border-radius: 6px;
  transition: 0.3s ease-in-out;
  margin: 30px auto 0;
}

.transaction-container:hover {
  transform: scale(1.03);
  width: 220px;
}

.transaction-container:hover .left-side {
  width: 100%;
}

.transaction-container .left-side {
  background-color: #5de2a3;
  width: 130px;
  height: 120px;
  border-radius: 4px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: 0.3s;
  flex-shrink: 0;
  overflow: hidden;
}

.transaction-container .right-side {
  width: calc(100% - 130px);
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  justify-content: space-between;
  white-space: nowrap;
  transition: 0.3s;
}

.transaction-container .right-side:hover {
  background-color: #f9f7f9;
}

.transaction-container .arrow {
  width: 20px;
  height: 20px;
  margin-right: 20px;
}

.transaction-container .new {
  font-size: 23px;
  font-family: 'Lexend Deca', sans-serif;
  margin-left: 20px;
  color: #333333;
}

.transaction-container .card {
  width: 70px;
  height: 46px;
  background-color: #c7ffbc;
  border-radius: 6px;
  position: absolute;
  display: flex;
  z-index: 10;
  flex-direction: column;
  align-items: center;
  -webkit-box-shadow: 9px 9px 9px -2px rgba(77, 200, 143, 0.72);
  -moz-box-shadow: 9px 9px 9px -2px rgba(77, 200, 143, 0.72);
  box-shadow: 9px 9px 9px -2px rgba(77, 200, 143, 0.72);
}

.transaction-container .card-line {
  width: 65px;
  height: 13px;
  background-color: #80ea69;
  border-radius: 2px;
  margin-top: 7px;
}

.transaction-container .buttons {
  width: 8px;
  height: 8px;
  background-color: #379e1f;
  box-shadow: 0 -10px 0 0 #26850e, 0 10px 0 0 #56be3e;
  border-radius: 50%;
  margin-top: 5px;
  transform: rotate(90deg);
  margin: 10px 0 0 -30px;
}

.transaction-container:hover .card {
  animation: slide-top 1.2s cubic-bezier(0.645, 0.045, 0.355, 1) both;
}

.transaction-container:hover .post {
  animation: slide-post 1s cubic-bezier(0.165, 0.84, 0.44, 1) both;
}

@keyframes slide-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-70px) rotate(90deg);
    transform: translateY(-70px) rotate(90deg);
  }
  60% {
    -webkit-transform: translateY(-70px) rotate(90deg);
    transform: translateY(-70px) rotate(90deg);
  }
  100% {
    -webkit-transform: translateY(-8px) rotate(90deg);
    transform: translateY(-8px) rotate(90deg);
  }
}

.transaction-container .post {
  width: 63px;
  height: 75px;
  background-color: #dddde0;
  position: absolute;
  z-index: 11;
  bottom: 10px;
  top: 120px;
  border-radius: 6px;
  overflow: hidden;
}

.transaction-container .post-line {
  width: 47px;
  height: 9px;
  background-color: #545354;
  position: absolute;
  border-radius: 0px 0px 3px 3px;
  right: 8px;
  top: 8px;
}

.transaction-container .post-line:before {
  content: '';
  position: absolute;
  width: 47px;
  height: 9px;
  background-color: #757375;
  top: -8px;
}

.transaction-container .screen {
  width: 47px;
  height: 23px;
  background-color: #ffffff;
  position: absolute;
  top: 22px;
  right: 8px;
  border-radius: 3px;
}

.transaction-container .numbers {
  width: 12px;
  height: 12px;
  background-color: #838183;
  box-shadow: 0 -18px 0 0 #838183, 0 18px 0 0 #838183;
  border-radius: 2px;
  position: absolute;
  transform: rotate(90deg);
  left: 25px;
  top: 52px;
}

.transaction-container .numbers-line2 {
  width: 12px;
  height: 12px;
  background-color: #aaa9ab;
  box-shadow: 0 -18px 0 0 #aaa9ab, 0 18px 0 0 #aaa9ab;
  border-radius: 2px;
  position: absolute;
  transform: rotate(90deg);
  left: 25px;
  top: 68px;
}

@keyframes slide-post {
  50% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(-70px);
    transform: translateY(-70px);
  }
}

.transaction-container .dollar {
  position: absolute;
  font-size: 16px;
  font-family: 'Lexend Deca', sans-serif;
  width: 100%;
  left: 0;
  top: 0;
  color: #4b953b;
  text-align: center;
}

.transaction-container:hover .dollar {
  animation: fade-in-fwd 0.3s 1s backwards;
}

@keyframes fade-in-fwd {
  0% {
    opacity: 0;
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@media only screen and (max-width: 480px) {
  .transaction-container {
    transform: scale(0.7);
  }
  .transaction-container:hover {
    transform: scale(0.74);
  }
  .transaction-container .new {
    font-size: 18px;
  }
}
</style>
