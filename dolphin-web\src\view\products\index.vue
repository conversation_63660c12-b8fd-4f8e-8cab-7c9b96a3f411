<template>
  <div id="Products">
    <!-- 产品中心标题区域 -->
    <div class="products-header">
      <!-- 背景视频 -->
      <div class="video-background">
        <video
          ref="backgroundVideo"
          autoplay
          muted
          loop
          playsinline
          class="background-video"
          @loadeddata="onVideoLoaded"
          @error="onVideoError"
        >
          <source src="@/assets/videos/products-bg.mp4" type="video/mp4" />
          <source src="@/assets/videos/products-bg.webm" type="video/webm" />
          <!-- 如果视频加载失败，显示备用图片 -->
          您的浏览器不支持视频播放
        </video>
        <!-- 视频遮罩层 -->
        <div class="video-overlay"></div>
      </div>

      <div class="container header-content">
        <h1 class="wow fadeInUp">产品中心</h1>
        <p class="wow fadeInUp" data-wow-delay="0.2s">
          基于深度学习的医疗超声AI解决方案，为医疗行业提供智能化诊断辅助工具
        </p>
      </div>
    </div>

    <!-- 产品展示区域 -->
    <div class="products-content">
      <div class="container">
        <div class="row justify-content-center">
          <!-- AI模型API -->
          <div
            class="col-lg-4 col-md-6 col-sm-12 d-flex justify-content-center"
          >
            <div class="product-3d-card wow fadeInUp" data-wow-delay="0.1s">
              <div class="card">
                <div class="logo">
                  <span class="circle circle1"></span>
                  <span class="circle circle2"></span>
                  <span class="circle circle3"></span>
                  <span class="circle circle4"></span>
                  <span class="circle circle5">
                    <img
                      src="@/assets/icons/cancer.png"
                      alt="AI模型"
                      class="logo-icon"
                    />
                  </span>
                </div>
                <div class="glass"></div>
                <div class="content">
                  <span class="title">医疗超声大模型API</span>
                  <span class="text"
                    >基于深度学习的医疗超声影像分析API，提供高精度的病灶检测、测量和诊断辅助功能</span
                  >
                </div>
                <div class="bottom">
                  <div class="features-container">
                    <div class="feature-item">高精度病灶检测</div>
                    <div class="feature-item">自动测量功能</div>
                    <div class="feature-item">实时分析处理</div>
                    <div class="feature-item">标准化接口</div>
                  </div>
                  <div class="view-more">
                    <router-link :to="'/products/api'" class="view-more-button"
                      >了解详情</router-link
                    >
                    <svg
                      class="svg"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 医疗影像工具 -->
          <div
            class="col-lg-4 col-md-6 col-sm-12 d-flex justify-content-center"
          >
            <div class="product-3d-card wow fadeInUp" data-wow-delay="0.2s">
              <div class="card">
                <div class="logo">
                  <span class="circle circle1"></span>
                  <span class="circle circle2"></span>
                  <span class="circle circle3"></span>
                  <span class="circle circle4"></span>
                  <span class="circle circle5">
                    <img
                      src="@/assets/icons/size.png"
                      alt="影像工具"
                      class="logo-icon"
                    />
                  </span>
                </div>
                <div class="glass"></div>
                <div class="content">
                  <span class="title">医疗影像工具</span>
                  <span class="text"
                    >专业的医疗影像处理和分析工具，支持多种影像格式，提供完整的影像处理解决方案</span
                  >
                </div>
                <div class="bottom">
                  <div class="features-container">
                    <div class="feature-item">多格式支持</div>
                    <div class="feature-item">智能增强</div>
                    <div class="feature-item">批量处理</div>
                    <div class="feature-item">云端存储</div>
                  </div>
                  <div class="view-more">
                    <router-link
                      :to="'/products/imaging'"
                      class="view-more-button"
                      >了解详情</router-link
                    >
                    <svg
                      class="svg"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 模型训练平台 -->
          <div
            class="col-lg-4 col-md-6 col-sm-12 d-flex justify-content-center"
          >
            <div class="product-3d-card wow fadeInUp" data-wow-delay="0.3s">
              <div class="card">
                <div class="logo">
                  <span class="circle circle1"></span>
                  <span class="circle circle2"></span>
                  <span class="circle circle3"></span>
                  <span class="circle circle4"></span>
                  <span class="circle circle5">
                    <img
                      src="@/assets/icons/training.png"
                      alt="训练平台"
                      class="logo-icon"
                    />
                  </span>
                </div>
                <div class="glass"></div>
                <div class="content">
                  <span class="title">模型训练平台</span>
                  <span class="text"
                    >专业的AI模型训练平台，提供从数据预处理到模型部署的完整机器学习工作流</span
                  >
                </div>
                <div class="bottom">
                  <div class="features-container">
                    <div class="feature-item">可视化训练</div>
                    <div class="feature-item">自动调参</div>
                    <div class="feature-item">模型评估</div>
                    <div class="feature-item">一键部署</div>
                  </div>
                  <div class="view-more">
                    <router-link
                      :to="'/products/platform'"
                      class="view-more-button"
                      >了解详情</router-link
                    >
                    <svg
                      class="svg"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path d="m6 9 6 6 6-6"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品优势区域 -->
    <div class="products-advantages">
      <div class="container">
        <h2
          class="section-title wow fadeInUp"
          data-wow-delay="0s"
          data-wow-duration="0.6s"
        >
          产品优势
        </h2>
        <div class="advantages-grid">
          <div
            class="advantage-card wow fadeInLeft"
            data-wow-delay="0.1s"
            data-wow-duration="0.8s"
          >
            <div class="advantage-img">
              <img
                src="@/assets/icons/support.png"
                alt="安全可靠"
                class="advantage-icon-img"
              />
            </div>
            <div class="advantage-textBox">
              <div class="advantage-textContent">
                <p class="advantage-h1">安全可靠</p>
              </div>
              <p class="advantage-p">符合医疗行业标准，数据安全有保障</p>
            </div>
          </div>

          <div
            class="advantage-card wow fadeInUp"
            data-wow-delay="0.3s"
            data-wow-duration="0.8s"
          >
            <div class="advantage-img">
              <img
                src="@/assets/icons/top.png"
                alt="高效精准"
                class="advantage-icon-img"
              />
            </div>
            <div class="advantage-textBox">
              <div class="advantage-textContent">
                <p class="advantage-h1">高效精准</p>
              </div>
              <p class="advantage-p">AI算法优化，处理速度快，准确率高</p>
            </div>
          </div>

          <div
            class="advantage-card wow fadeInDown"
            data-wow-delay="0.5s"
            data-wow-duration="0.8s"
          >
            <div class="advantage-img">
              <img
                src="@/assets/icons/service.png"
                alt="专业服务"
                class="advantage-icon-img"
              />
            </div>
            <div class="advantage-textBox">
              <div class="advantage-textContent">
                <p class="advantage-h1">专业服务</p>
              </div>
              <p class="advantage-p">专业技术团队，提供全方位技术支持</p>
            </div>
          </div>

          <div
            class="advantage-card wow fadeInRight"
            data-wow-delay="0.7s"
            data-wow-duration="0.8s"
          >
            <div class="advantage-img">
              <img
                src="@/assets/icons/solve.png"
                alt="灵活扩展"
                class="advantage-icon-img"
              />
            </div>
            <div class="advantage-textBox">
              <div class="advantage-textContent">
                <p class="advantage-h1">灵活扩展</p>
              </div>
              <p class="advantage-p">模块化设计，支持定制化开发</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Products">
import WOW from 'wow.js'
import { onMounted, ref } from 'vue'

const backgroundVideo = ref(null)

// 视频加载完成事件
const onVideoLoaded = () => {
  console.log('背景视频加载完成')
}

// 视频加载错误事件
const onVideoError = () => {
  console.log('背景视频加载失败，使用备用背景')
  // 如果视频加载失败，可以隐藏视频容器，显示备用背景
  if (backgroundVideo.value) {
    backgroundVideo.value.style.display = 'none'
  }
}

onMounted(() => {
  new WOW({
    boxClass: 'wow',
    animateClass: 'animated',
    offset: 100, // 提前100px触发动画
    mobile: true,
    live: true,
    resetAnimation: true // 允许重复动画
  }).init()
})
</script>

<style scoped>
#Products {
  padding: 0;
}

/* 产品中心标题区域 */
.products-header {
  position: relative;
  color: white;
  padding: 30px 0 80px 0;
  text-align: center;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 备用背景，当视频加载失败时显示 */
  /* background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%); */
}

/* 视频背景容器 */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

/* 背景视频样式 */
.background-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translate(-50%, -50%);
  object-fit: cover;
  z-index: -1;
}

/* 视频遮罩层 */
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: linear-gradient(135deg, rgba(44, 90, 160, 0.7) 0%, rgba(30, 61, 114, 0.8) 100%); */
  z-index: -1;
}

/* 头部内容容器 */
.header-content {
  position: relative;
  z-index: 1;
}

.products-header h1 {
  font-size: 50px;
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 0;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.95);
}

.products-header p {
  font-size: 20px;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  margin-top: 1%;
  font-weight: 500;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.9);
}

/* 产品展示区域 */
.products-content {
  padding: 80px 0;
  background: #f8f9fa;
}

/* 3D产品卡片样式 */
.product-3d-card {
  width: 320px;
  height: 400px;
  perspective: 1000px;
  margin: 20px auto;
}

.product-3d-card .card {
  height: 100%;
  border-radius: 50px;
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  transition: all 0.5s ease-in-out;
  transform-style: preserve-3d;
  box-shadow: rgba(30, 61, 114, 0) 40px 50px 25px -40px,
    rgba(30, 61, 114, 0.2) 0px 25px 25px -5px;
}

.product-3d-card .glass {
  transform-style: preserve-3d;
  position: absolute;
  inset: 8px;
  border-radius: 55px;
  border-top-right-radius: 100%;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.349) 0%,
    rgba(255, 255, 255, 0.815) 100%
  );
  transform: translate3d(0px, 0px, 25px);
  border-left: 1px solid white;
  border-bottom: 1px solid white;
  transition: all 0.5s ease-in-out;
}

.product-3d-card .content {
  padding: 100px 40px 0px 30px;
  transform: translate3d(0, 0, 26px);
}

.product-3d-card .content .title {
  display: block;
  color: #1e3d72;
  font-weight: 900;
  font-size: 18px;
  line-height: 1.3;
  margin-bottom: 15px;
}

.product-3d-card .content .text {
  display: block;
  color: rgba(30, 61, 114, 0.8);
  font-size: 14px;
  line-height: 1.4;
  margin-top: 15px;
}

.product-3d-card .bottom {
  padding: 15px 20px;
  transform-style: preserve-3d;
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  transform: translate3d(0, 0, 26px);
}

.product-3d-card .features-container {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.product-3d-card .feature-item {
  color: rgba(30, 61, 114, 0.9);
  font-size: 12px;
  padding: 3px 0;
  position: relative;
  padding-left: 15px;
}

.product-3d-card .feature-item:before {
  content: '✓';
  color: #2c5aa0;
  font-weight: bold;
  position: absolute;
  left: 0;
  font-size: 10px;
}

.product-3d-card .view-more {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.product-3d-card .view-more:hover {
  transform: translate3d(0, 0, 10px);
}

.product-3d-card .view-more-button {
  background: none;
  border: none;
  color: #2c5aa0;
  font-weight: bolder;
  font-size: 14px;
  text-decoration: none;
  margin-right: 8px;
}

.product-3d-card .view-more-button:hover {
  color: #1e3d72;
  text-decoration: none;
}

.product-3d-card .view-more .svg {
  fill: none;
  stroke: #2c5aa0;
  stroke-width: 3px;
  max-height: 15px;
  width: 15px;
}

.product-3d-card .logo {
  position: absolute;
  right: 0;
  top: 0;
  transform-style: preserve-3d;
}

.product-3d-card .logo .circle {
  display: block;
  position: absolute;
  aspect-ratio: 1;
  border-radius: 50%;
  top: 0;
  right: 0;
  box-shadow: rgba(100, 100, 111, 0.2) -10px 10px 20px 0px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background: rgba(44, 90, 160, 0.2);
  transition: all 0.5s ease-in-out;
}

.product-3d-card .logo .circle1 {
  width: 170px;
  transform: translate3d(0, 0, 20px);
  top: 8px;
  right: 8px;
}

.product-3d-card .logo .circle2 {
  width: 140px;
  transform: translate3d(0, 0, 40px);
  top: 10px;
  right: 10px;
  -webkit-backdrop-filter: blur(1px);
  backdrop-filter: blur(1px);
  transition-delay: 0.4s;
}

.product-3d-card .logo .circle3 {
  width: 110px;
  transform: translate3d(0, 0, 60px);
  top: 17px;
  right: 17px;
  transition-delay: 0.8s;
}

.product-3d-card .logo .circle4 {
  width: 80px;
  transform: translate3d(0, 0, 80px);
  top: 23px;
  right: 23px;
  transition-delay: 1.2s;
}

.product-3d-card .logo .circle5 {
  width: 50px;
  transform: translate3d(0, 0, 100px);
  top: 30px;
  right: 30px;
  display: grid;
  place-content: center;
  transition-delay: 1.6s;
  background: rgba(44, 90, 160, 0.3);
}

.product-3d-card .logo .logo-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(100%);
}

/* 悬停效果 */
.product-3d-card:hover .card {
  transform: rotate3d(1, 1, 0, 30deg);
  box-shadow: rgba(30, 61, 114, 0.3) 30px 50px 25px -40px,
    rgba(30, 61, 114, 0.1) 0px 25px 30px 0px;
}

.product-3d-card:hover .card .logo .circle2 {
  transform: translate3d(0, 0, 60px);
}

.product-3d-card:hover .card .logo .circle3 {
  transform: translate3d(0, 0, 80px);
}

.product-3d-card:hover .card .logo .circle4 {
  transform: translate3d(0, 0, 100px);
}

.product-3d-card:hover .card .logo .circle5 {
  transform: translate3d(0, 0, 120px);
}

/* 产品优势区域 */
.products-advantages {
  padding: 80px 0;
  background: #f8f9fa;
}

/* 自定义动画关键帧 */
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(50px) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0);
  }
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 60px;
  font-weight: bold;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.advantage-card {
  width: 100%;
  height: 70px;
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: left;
  backdrop-filter: blur(10px);
  transition: all 0.5s ease-in-out;
  margin: 0 auto;
  box-shadow: 0 4px 15px rgba(44, 90, 160, 0.2);
  /* 动画前的初始状态 */
  visibility: hidden;
}

/* WOW.js 动画激活后的状态 */
.advantage-card.wow {
  visibility: visible;
}

.advantage-card:hover {
  cursor: pointer;
  transform: scale(1.05);
  background: linear-gradient(135deg, #3a6bb8 0%, #2a4a85 100%);
  box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
}

/* 增强动画效果 */
.advantage-card.animated.fadeInLeft,
.advantage-card.animated.fadeInRight,
.advantage-card.animated.fadeInUp,
.advantage-card.animated.fadeInDown {
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.advantage-img {
  width: 50px;
  height: 50px;
  margin-left: 10px;
  border-radius: 10px;
  background: linear-gradient(135deg, #ffffff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

.advantage-card:hover > .advantage-img {
  transition: 0.5s ease-in-out;
  background: linear-gradient(135deg, #f0f8ff 0%, #d4e8ff 100%);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.advantage-icon-img {
  width: 25px;
  height: 25px;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(1234%)
    hue-rotate(201deg) brightness(94%) contrast(90%);
}

.advantage-textBox {
  width: calc(100% - 90px);
  margin-left: 10px;
  color: white;
  font-family: 'Poppins', sans-serif;
}

.advantage-textContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.advantage-h1 {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.advantage-p {
  font-size: 12px;
  font-weight: lighter;
  margin: 2px 0 0 0;
  line-height: 1.3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .products-header {
    padding: 50px 0;
    min-height: 70vh;
  }

  .products-header h1 {
    font-size: 30px;
    margin-top: 15%;
  }

  .products-header p {
    font-size: 16px;
    margin-top: 2%;
  }

  .products-content {
    padding: 50px 0;
  }

  .product-3d-card {
    width: 280px;
    height: 360px;
  }

  .product-3d-card .content {
    padding: 80px 30px 0px 25px;
  }

  .product-3d-card .content .title {
    font-size: 16px;
  }

  .product-3d-card .content .text {
    font-size: 13px;
  }

  .products-advantages {
    padding: 50px 0;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .advantage-card {
    height: 80px;
  }

  .advantage-textBox {
    width: calc(100% - 80px);
  }

  .advantage-h1 {
    font-size: 14px;
  }

  .advantage-p {
    font-size: 11px;
  }

  /* 移动端视频优化 */
  .background-video {
    /* 在移动设备上可能需要调整视频显示 */
    object-fit: cover;
  }
}

/* 平板设备媒体查询 */
@media (max-width: 992px) {
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* 针对低性能设备的媒体查询 */
@media (max-width: 480px) {
  .products-header {
    min-height: 60vh;
  }

  .products-header h1 {
    font-size: 24px;
    margin-top: 12%;
  }

  .products-header p {
    font-size: 14px;
    margin-top: 3%;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .advantage-card {
    height: 75px;
  }

  .advantage-h1 {
    font-size: 13px;
  }

  .advantage-p {
    font-size: 10px;
  }

  .product-3d-card {
    width: 260px;
    height: 340px;
  }

  .product-3d-card .content {
    padding: 70px 25px 0px 20px;
  }

  .product-3d-card .content .title {
    font-size: 15px;
  }

  .product-3d-card .content .text {
    font-size: 12px;
  }

  .product-3d-card .logo .circle1 {
    width: 140px;
  }

  .product-3d-card .logo .circle2 {
    width: 120px;
  }

  .product-3d-card .logo .circle3 {
    width: 100px;
  }

  .product-3d-card .logo .circle4 {
    width: 70px;
  }

  .product-3d-card .logo .circle5 {
    width: 45px;
  }

  /* 在小屏幕设备上可以选择隐藏视频以提升性能 */
  .background-video {
    display: block; /* 保持显示，如需隐藏可改为 none */
  }
}
</style>
